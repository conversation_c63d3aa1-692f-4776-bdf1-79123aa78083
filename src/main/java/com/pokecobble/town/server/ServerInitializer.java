package com.pokecobble.town.server;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.PlayerDataManager;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.config.BackupConfig;
import com.pokecobble.town.data.PlayerDataUtils;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.network.chunk.ChunkDataSynchronizer;
import com.pokecobble.town.network.player.PlayerDataSynchronizer;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import net.fabricmc.api.DedicatedServerModInitializer;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.minecraft.server.MinecraftServer;

import java.util.Collection;
import net.minecraft.server.network.ServerPlayerEntity;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;

/**
 * Initializes server-side components of the mod.
 * This class is responsible for setting up the server instance in the TownManager
 * and registering server lifecycle events.
 */
public class ServerInitializer implements DedicatedServerModInitializer {
    // Track the last backup time
    private static Instant lastBackupTime = Instant.now();

    // Flag to indicate if the server is shutting down
    private static volatile boolean isShuttingDown = false;
    @Override
    public void onInitializeServer() {
        Pokecobbleclaim.LOGGER.info("Initializing PokeCobbleClaim server components");

        // Load backup configuration
        BackupConfig.load();

        // Register server start event
        ServerLifecycleEvents.SERVER_STARTING.register(this::onServerStarting);

        // Register server started event (when fully ready)
        ServerLifecycleEvents.SERVER_STARTED.register(this::onServerStarted);

        // Register server stop event
        ServerLifecycleEvents.SERVER_STOPPING.register(this::onServerStopping);

        // Register player join event
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            ServerPlayerEntity player = handler.getPlayer();
            UUID playerId = player.getUuid();

            // Handle player join in PlayerDataManager
            PlayerDataManager.getInstance().onPlayerJoin(player);

            // When a player joins, synchronize town and chunk data to them
            TownManager.getInstance().synchronizeTownData();
            ChunkDataSynchronizer.syncChunkData(server);

            // Send the current town list to the new player
            // This ensures they see all existing towns immediately, including loaded towns
            TownDataSynchronizer.sendTownListToNewPlayer(player);

            // Send global chunk data to the new player immediately
            // This ensures they see all existing chunk claims right away
            com.pokecobble.town.network.chunk.GlobalChunkSyncHandler.sendGlobalChunkData(player, server);

            // Sync player permissions immediately when they connect
            com.pokecobble.town.permission.PermissionManager.getInstance().syncAllPermissionsToClient(player);

            // Sync claim tags immediately when they connect
            com.pokecobble.town.claim.ClaimTagManager.getInstance().syncAllClaimTagsToClient(player);

            // Sync town requests immediately when they connect
            com.pokecobble.town.network.town.TownRequestNetworkHandler.syncRequestsToPlayer(player);

            Pokecobbleclaim.LOGGER.debug("Sent initial data sync to player {}", player.getName().getString());

            // Ensure all town data is synchronized to the new player
            // This is critical for showing loaded towns after server restart
            server.execute(() -> {
                // Sync all town data to the new player
                TownDataSynchronizer.syncPlayerTownData(server, playerId);

                // Note: Player data synchronization is handled in PlayerDataManager.onPlayerJoin
                // to avoid race conditions and ensure proper data loading order

                // Ensure player's town membership is synchronized
                UUID townId = TownManager.getInstance().getPlayerTownId(playerId);
                if (townId != null) {
                    // Player is in a town, ensure this is synchronized to client
                    Town playerTown = TownManager.getInstance().getTownById(townId);
                    if (playerTown != null) {
                        // Force synchronization of the player's town data, especially player ranks
                        TownDataSynchronizer.syncTownData(server, playerTown);

                        // Send claim tag data to the new player if they're in a town
                        try {
                            List<com.pokecobble.town.claim.ClaimTag> townTags = playerTown.getClaimTags();
                            if (townTags != null && !townTags.isEmpty()) {
                                // Send claim tag data to the player
                                com.pokecobble.town.network.chunk.ClaimTagSyncHandler.sendClaimTagDataResponse(player, townTags);
                                Pokecobbleclaim.LOGGER.info("Sent {} claim tags to new player {} for town {}",
                                    townTags.size(), player.getName().getString(), playerTown.getName());
                            } else {
                                Pokecobbleclaim.LOGGER.debug("No claim tags to send to new player {} for town {}",
                                    player.getName().getString(), playerTown.getName());
                            }
                        } catch (Exception e) {
                            Pokecobbleclaim.LOGGER.error("Failed to send claim tag data to new player {}: {}",
                                player.getName().getString(), e.getMessage());
                        }

                        // Synchronize town settings to the player with a small delay to ensure all data is loaded
                        server.execute(() -> {
                            try {
                                // Small delay to ensure all town data is fully loaded
                                Thread.sleep(100);
                                com.pokecobble.config.ConfigSynchronizer.syncTownSettingsToPlayer(server, playerId);
                                Pokecobbleclaim.LOGGER.info("Synchronized town settings for player " + player.getName().getString() + " to town " + townId);
                            } catch (Exception e) {
                                Pokecobbleclaim.LOGGER.warn("Failed to sync town settings to player " + player.getName().getString() + ": " + e.getMessage());
                            }
                        });

                        Pokecobbleclaim.LOGGER.info("Synchronized town membership and player ranks for player " + player.getName().getString() + " to town " + townId);
                    }
                } else {
                    Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " is not in any town");

                    // Even if player is not in a town, sync all town settings so they can see all towns properly
                    server.execute(() -> {
                        try {
                            Thread.sleep(100);
                            com.pokecobble.config.ConfigSynchronizer.syncTownSettingsToPlayer(server, playerId);
                            Pokecobbleclaim.LOGGER.info("Synchronized all town settings for player " + player.getName().getString() + " (not in any town)");
                        } catch (Exception e) {
                            Pokecobbleclaim.LOGGER.warn("Failed to sync all town settings to player " + player.getName().getString() + ": " + e.getMessage());
                        }
                    });
                }

                // Log the number of towns available for this player
                int townCount = TownManager.getInstance().getAllTowns().size();
                Pokecobbleclaim.LOGGER.info("Synchronized " + townCount + " towns to player " + player.getName().getString());

                // Sync food price timer data to the player
                com.pokecobble.phone.food.FoodPriceTimer.getInstance().syncTimerToPlayer(player);

                // Sync current food prices to the player
                com.pokecobble.phone.food.FoodPriceManager.getInstance().syncPricesToPlayer(player);
            });
        });

        // Register player leave event
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.DISCONNECT.register((handler, server) -> {
            ServerPlayerEntity player = handler.getPlayer();
            UUID playerId = player.getUuid();

            // Handle player leave in PlayerDataManager
            PlayerDataManager.getInstance().onPlayerLeave(player);

            // When a player leaves, remove them from version tracking
            com.pokecobble.town.network.town.TownDataSynchronizer.removePlayer(playerId);
            ChunkDataSynchronizer.removePlayer(playerId);
            PlayerDataSynchronizer.removePlayer(playerId);

            // Clear status subscriptions
            com.pokecobble.status.RealTimeStatusManager.getInstance().clearPlayerSubscriptions(playerId);

            // Clear config subscriptions
            com.pokecobble.config.ConfigSynchronizer.clearPlayerConfig(playerId);

            // Clean up claim tag screen active users for all towns
            cleanupClaimTagActiveUsers(playerId, server);
        });

        Pokecobbleclaim.LOGGER.info("PokeCobbleClaim server components initialized successfully");
    }

    /**
     * Cleans up claim tag active users when a player disconnects.
     * This prevents "Unknown Player" from appearing in the multi-user display.
     */
    private static void cleanupClaimTagActiveUsers(UUID playerId, MinecraftServer server) {
        try {
            com.pokecobble.town.claim.ClaimTagManager claimTagManager =
                com.pokecobble.town.claim.ClaimTagManager.getInstance();

            // Get all towns and check if the player was active in any claim tag screens
            com.pokecobble.town.TownManager townManager = com.pokecobble.town.TownManager.getInstance();
            for (com.pokecobble.town.Town town : townManager.getAllTowns()) {
                if (town != null && town.getPlayers().contains(playerId)) {
                    // Check if player was in active users for this town's claim tag screen
                    java.util.Set<UUID> activeUsers = claimTagManager.getActiveUsers(town.getId());
                    if (activeUsers.contains(playerId)) {
                        // Remove the player from active users
                        claimTagManager.removeActiveUser(town.getId(), playerId);

                        // Sync updated active users list to all town members
                        claimTagManager.syncActiveUsersToTown(town.getId(), server);

                        Pokecobbleclaim.LOGGER.debug("Cleaned up claim tag active user {} for town {} on disconnect",
                            playerId, town.getId());
                    }
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error cleaning up claim tag active users for disconnected player {}: {}",
                playerId, e.getMessage());
        }
    }

    /**
     * Called when the server is starting.
     * Sets the server instance in the TownManager.
     *
     * @param server The server instance
     */
    private void onServerStarting(MinecraftServer server) {
        Pokecobbleclaim.LOGGER.info("Setting server instance in TownManager and PlayerDataManager");

        // Set server instance in main Pokecobbleclaim class
        Pokecobbleclaim.setServer(server);

        // Set server instance in TownManager
        TownManager.getInstance().setServer(server);

        // Set server instance in PlayerDataManager
        PlayerDataManager.getInstance().setServer(server);

        // Initialize TownRequestManager
        com.pokecobble.town.TownRequestManager.getInstance().initialize(server);

        // Set server instance in ErrorLogger
        ErrorLogger.getInstance().setServer(server);
        ErrorLogger.getInstance().logInfo("Server started", "Server");

        // Initialize real-time status manager
        com.pokecobble.status.RealTimeStatusManager.getInstance().initialize(server);

        // Initialize permission manager
        Pokecobbleclaim.LOGGER.info("Initializing file-based permission manager");
        com.pokecobble.town.permission.PermissionManager.getInstance();

        // Register server-side permission network handlers
        com.pokecobble.town.permission.PermissionManager.registerServerNetworkHandlers();

        // Initialize economy system with proper server instance
        Pokecobbleclaim.LOGGER.info("Initializing economy system with server instance");
        try {
            com.pokecobble.economy.manager.EconomyManager.getInstance().initialize(server);
            Pokecobbleclaim.LOGGER.info("Economy system initialized successfully with server");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize economy system with server", e);
        }

        // Initialize town bank system
        Pokecobbleclaim.LOGGER.info("Initializing town bank system");
        try {
            com.pokecobble.town.bank.TownBankManager.getInstance().initialize(server);
            Pokecobbleclaim.LOGGER.info("Town bank system initialized successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize town bank system", e);
        }

        // Initialize food price system
        Pokecobbleclaim.LOGGER.info("Initializing food price management system");
        com.pokecobble.phone.food.FoodPriceManager.getInstance().initialize(server);
        com.pokecobble.phone.food.FoodPriceTimer.getInstance().initialize(server);

        // Run quick validation test
        com.pokecobble.phone.food.test.FoodPriceSystemTest.runQuickValidation();

        // DISABLED: Old claim tag manager - replaced by Permission System v2
        // Pokecobbleclaim.LOGGER.info("Initializing file-based claim tag manager");
        // com.pokecobble.town.claim.ClaimTagManager.getInstance();

        // Register server-side claim tag network handlers
        com.pokecobble.town.claim.ClaimTagManager.registerServerNetworkHandlers();

        // Load town data from disk
        // This is critical for restoring player-town relationships after server restart
        Pokecobbleclaim.LOGGER.info("Loading town data during server startup");
        com.pokecobble.town.data.TownDataStorage.loadTowns();

        // CRITICAL: After loading towns, synchronize claim tags to ensure ID consistency
        Pokecobbleclaim.LOGGER.info("Synchronizing claim tag IDs after town loading");
        synchronizeClaimTagIds();

        // Sync loaded player data to PlayerDataManager to ensure consistency
        Pokecobbleclaim.LOGGER.info("Syncing loaded player data to PlayerDataManager");
        syncLoadedPlayerDataToManager();

        // Migrate existing claim data if needed
        Pokecobbleclaim.LOGGER.info("Checking for claim data migration during server startup");
        if (com.pokecobble.town.claim.ClaimDataMigration.isMigrationNeeded()) {
            Pokecobbleclaim.LOGGER.info("Migrating claim data from old format to new file-based system");
            boolean migrationSuccess = com.pokecobble.town.claim.ClaimDataMigration.migrateClaimData();
            if (migrationSuccess) {
                Pokecobbleclaim.LOGGER.info("Claim data migration completed successfully");
                // Validate migration
                if (com.pokecobble.town.claim.ClaimDataMigration.validateMigration()) {
                    Pokecobbleclaim.LOGGER.info("Migration validation passed");
                    // Clean up old files after successful migration
                    com.pokecobble.town.claim.ClaimDataMigration.cleanupOldFiles();
                } else {
                    Pokecobbleclaim.LOGGER.warn("Migration validation failed, keeping old files as backup");
                }
            } else {
                Pokecobbleclaim.LOGGER.error("Claim data migration failed, falling back to old system");
            }
        }

        // Load chunk claim data from new file-based system
        Pokecobbleclaim.LOGGER.info("Loading chunk claim data during server startup");
        com.pokecobble.town.chunk.TownChunkDataManager.getInstance().loadAllTownData();

        // Load claim history data from files
        Pokecobbleclaim.LOGGER.info("Loading claim history data during server startup");
        com.pokecobble.town.claim.SimpleClaimHistoryManager.getInstance().loadAllHistoriesFromFiles();

        // Simple permission system doesn't need special server data loading notification
        // Chunk ownership data is loaded directly when needed

        // Perform health check on town settings files before loading
        // This repairs any corrupted or invalid settings files
        Pokecobbleclaim.LOGGER.info("Performing town settings health check during server startup");
        com.pokecobble.town.config.TownSettingsManager.performSettingsHealthCheck();

        // Load all town settings from disk to ensure they're available in memory
        // This is critical for settings persistence across server restarts
        Pokecobbleclaim.LOGGER.info("Loading town settings during server startup");
        com.pokecobble.town.config.TownSettingsManager.loadAllTownSettingsFromDisk();

        // Apply persisted settings to all loaded towns
        // This ensures town objects reflect the persisted settings after server restart
        Pokecobbleclaim.LOGGER.info("Applying persisted settings to all loaded towns");
        com.pokecobble.town.config.TownSettingsManager.applyPersistedSettingsToAllTowns();

        // After loading towns, ensure all town data is ready for synchronization
        // Broadcast town list to prepare for when players join
        TownManager townManager = TownManager.getInstance();
        if (!townManager.getAllTowns().isEmpty()) {
            Pokecobbleclaim.LOGGER.info("Preparing town data synchronization for " + townManager.getAllTowns().size() + " loaded towns");

            // Verify that town settings were properly loaded for all towns
            int townsWithSettings = 0;
            int townsWithCustomSettings = 0;
            int townsWithLoadedSettings = 0;
            for (com.pokecobble.town.Town town : townManager.getAllTowns()) {
                // Check if town has any existing settings (not defaults)
                java.util.Map<String, Object> existingSettings = com.pokecobble.town.config.TownSettingsManager.getExistingTownSettings(town.getId());
                if (existingSettings != null && !existingSettings.isEmpty()) {
                    townsWithCustomSettings++;
                    townsWithLoadedSettings++;
                }

                // This will create defaults if none exist, but we want to track actual loaded settings
                java.util.Map<String, Object> settings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());
                if (settings != null && !settings.isEmpty()) {
                    townsWithSettings++;
                }
            }
            Pokecobbleclaim.LOGGER.info("Town settings status: " + townsWithSettings + " total, " + townsWithLoadedSettings + " loaded from disk, " + townsWithCustomSettings + " with custom settings out of " + townManager.getAllTowns().size() + " towns");

            // Note: Town settings will be synced individually when players join
            // This avoids massive startup sync overhead
            Pokecobbleclaim.LOGGER.info("Town settings loaded and ready for player-specific synchronization");
        } else {
            Pokecobbleclaim.LOGGER.info("No towns were loaded from disk - starting with empty town list");
        }

        // Reset all player data versions
        TownManager.getInstance().resetAllPlayerDataVersions();
        PlayerDataSynchronizer.clearVersionTracking();

        // Register periodic town data synchronization
        registerPeriodicSync(server);
    }

    /**
     * Synchronizes claim tag IDs between ClaimTagManager and Town objects after server startup.
     * This ensures that chunk-to-tag mappings remain valid after server restart.
     */
    private static void synchronizeClaimTagIds() {
        try {
            com.pokecobble.town.claim.ClaimTagManager tagManager =
                com.pokecobble.town.claim.ClaimTagManager.getInstance();

            Collection<com.pokecobble.town.Town> allTowns =
                com.pokecobble.town.TownManager.getInstance().getAllTowns();

            int synchronizedTowns = 0;

            for (com.pokecobble.town.Town town : allTowns) {
                UUID townId = town.getId();

                // Get tags from ClaimTagManager (authoritative source)
                List<com.pokecobble.town.claim.ClaimTag> authoritativeTags = tagManager.getClaimTags(townId);

                // Get tags from Town object (potentially with different IDs)
                List<com.pokecobble.town.claim.ClaimTag> townTags = town.getClaimTags();

                if (authoritativeTags != null && !authoritativeTags.isEmpty()) {
                    // Update Town object with authoritative tags (preserving IDs)
                    town.updateClaimTags(authoritativeTags);
                    synchronizedTowns++;

                    Pokecobbleclaim.LOGGER.debug("Synchronized {} claim tags for town {} ({})",
                        authoritativeTags.size(), town.getName(), townId);
                } else if (townTags != null && !townTags.isEmpty()) {
                    // No ClaimTagManager file exists, but Town has tags
                    // Save Town tags to ClaimTagManager to establish authoritative source
                    tagManager.updateClaimTags(townId, townTags, null);

                    Pokecobbleclaim.LOGGER.debug("Established ClaimTagManager authority for town {} with {} tags",
                        town.getName(), townTags.size());
                }
            }

            Pokecobbleclaim.LOGGER.info("Synchronized claim tag IDs for {} towns", synchronizedTowns);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing claim tag IDs: " + e.getMessage(), e);
        }
    }

    /**
     * Syncs the player data that was loaded with towns to the PlayerDataManager.
     * This ensures that PlayerDataManager has the same data that was loaded during town loading.
     */
    private void syncLoadedPlayerDataToManager() {
        try {
            TownManager townManager = TownManager.getInstance();
            PlayerDataManager playerDataManager = PlayerDataManager.getInstance();
            int totalPlayersSynced = 0;

            for (com.pokecobble.town.Town town : townManager.getAllTowns()) {
                for (UUID playerId : town.getPlayers()) {
                    com.pokecobble.town.TownPlayer townPlayer = town.getPlayer(playerId);
                    if (townPlayer != null) {
                        // Add the loaded player data to PlayerDataManager
                        playerDataManager.addPlayer(townPlayer);
                        totalPlayersSynced++;

                        Pokecobbleclaim.LOGGER.debug("Synced player data for " + townPlayer.getName() + " to PlayerDataManager");
                    }
                }
            }

            Pokecobbleclaim.LOGGER.info("Synced " + totalPlayersSynced + " players to PlayerDataManager");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing player data to PlayerDataManager: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Called when the server has fully started and is ready.
     * Performs final initialization tasks.
     *
     * @param server The server instance
     */
    private void onServerStarted(MinecraftServer server) {
        Pokecobbleclaim.LOGGER.info("Server fully started, performing final initialization");

        // Sync price data to any players who might already be connected
        try {
            com.pokecobble.phone.food.FoodPriceManager.getInstance().syncToAllPlayers();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing price data on server start: " + e.getMessage());
        }

        Pokecobbleclaim.LOGGER.info("Server startup initialization complete");
    }

    /**
     * Called when the server is stopping.
     * Clears the server instance in the TownManager.
     *
     * @param server The server instance
     */
    private void onServerStopping(MinecraftServer server) {
        Pokecobbleclaim.LOGGER.info("Saving all data and clearing server instances");

        // Set shutdown flag to stop periodic operations
        isShuttingDown = true;

        try {
            // Save all player data
            PlayerDataManager.getInstance().saveAllPlayers();
            Pokecobbleclaim.LOGGER.info("Player data saved successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving player data during shutdown: " + e.getMessage());
        }

        try {
            // Shutdown food price system
            com.pokecobble.phone.food.FoodPriceManager.getInstance().shutdown();
            com.pokecobble.phone.food.FoodPriceTimer.getInstance().shutdown();
            Pokecobbleclaim.LOGGER.info("Food price system shut down successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error shutting down food price system: " + e.getMessage());
        }

        try {
            // Save all town and player-town relationship data
            TownManager.getInstance().saveAllData();
            Pokecobbleclaim.LOGGER.info("Town data saved successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving town data during shutdown: " + e.getMessage());
        }

        try {
            // Save all chunk claim data to new file-based system
            com.pokecobble.town.chunk.TownChunkDataManager.getInstance().saveAllTownData();
            Pokecobbleclaim.LOGGER.info("Chunk claim data saved successfully");

            // Save all claim history data to files
            com.pokecobble.town.claim.SimpleClaimHistoryManager.getInstance().saveAllHistoriesToFiles();
            Pokecobbleclaim.LOGGER.info("Claim history data saved successfully");

            // Create backup of claim data
            String backupPath = com.pokecobble.town.claim.ClaimDataBackup.createBackup();
            if (backupPath != null) {
                Pokecobbleclaim.LOGGER.info("Created claim data backup: " + backupPath);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving chunk claim data during shutdown: " + e.getMessage());
        }

        // Create backup if enabled
        try {
            if (BackupConfig.isBackupOnServerStopEnabled()) {
                Pokecobbleclaim.LOGGER.info("Creating player data backup on server stop");
                String backupPath = PlayerDataUtils.backupAllPlayerData();
                if (backupPath != null) {
                    Pokecobbleclaim.LOGGER.info("Backup created at: " + backupPath);

                    // Clean up old backups
                    int deleted = PlayerDataUtils.cleanupOldBackups(BackupConfig.getMaxBackups());
                    if (deleted > 0) {
                        Pokecobbleclaim.LOGGER.info("Cleaned up " + deleted + " old backups");
                    }
                } else {
                    Pokecobbleclaim.LOGGER.warn("Failed to create backup on server stop");
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during backup process on server stop: " + e.getMessage());
        }

        // Shutdown real-time status manager
        try {
            com.pokecobble.status.RealTimeStatusManager.getInstance().shutdown();
            Pokecobbleclaim.LOGGER.info("Real-time status manager shut down successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Error shutting down real-time status manager: " + e.getMessage());
        }

        // Shutdown synchronization manager
        try {
            com.pokecobble.town.network.SynchronizationManager.getInstance().shutdown();
            Pokecobbleclaim.LOGGER.info("Synchronization manager shut down successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Error shutting down synchronization manager: " + e.getMessage());
        }

        // Shutdown backup executor
        try {
            com.pokecobble.town.data.PlayerDataUtils.shutdownBackupExecutor();
            Pokecobbleclaim.LOGGER.info("Backup executor shut down successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Error shutting down backup executor: " + e.getMessage());
        }

        // Give a moment for all shutdown operations to complete
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Clear town chat data (temporary storage)
        try {
            com.pokecobble.town.chat.TownChatManager.getInstance().clearAllChatData();
            Pokecobbleclaim.LOGGER.info("Town chat data cleared");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error clearing town chat data: " + e.getMessage());
        }

        // Clear server instance in main Pokecobbleclaim class
        try {
            Pokecobbleclaim.setServer(null);
            Pokecobbleclaim.LOGGER.info("Main Pokecobbleclaim server instance cleared");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error clearing main Pokecobbleclaim server instance: " + e.getMessage());
        }

        // Clear server instance in TownManager
        try {
            TownManager.getInstance().setServer(null);
            Pokecobbleclaim.LOGGER.info("TownManager server instance cleared");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error clearing TownManager server instance: " + e.getMessage());
        }

        // Clear server instance in PlayerDataManager
        try {
            PlayerDataManager.getInstance().setServer(null);
            Pokecobbleclaim.LOGGER.info("PlayerDataManager server instance cleared");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error clearing PlayerDataManager server instance: " + e.getMessage());
        }

        Pokecobbleclaim.LOGGER.info("PokeCobbleClaim server shutdown completed");
    }

    /**
     * Registers periodic town data synchronization.
     * This ensures that town data is synchronized even if no explicit changes are made.
     *
     * @param server The server instance
     */
    private void registerPeriodicSync(MinecraftServer server) {
        // Register a tick callback to periodically synchronize town data
        net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents.END_SERVER_TICK.register(s -> {
            // Skip all operations if server is shutting down
            if (isShuttingDown) {
                return;
            }

            // Synchronize town, chunk, and player data every 5 minutes (6000 ticks)
            if (s.getTicks() % 6000 == 0) {
                TownManager.getInstance().synchronizeTownData();
                ChunkDataSynchronizer.syncChunkData(s);
                PlayerDataSynchronizer.syncAllPlayerData(s);

                // Save all player data to disk
                PlayerDataManager.getInstance().saveAllPlayers();

                // Check if it's time for a backup
                if (BackupConfig.isAutoBackupEnabled()) {
                    Instant now = Instant.now();
                    long hoursSinceLastBackup = ChronoUnit.HOURS.between(lastBackupTime, now);

                    if (hoursSinceLastBackup >= BackupConfig.getBackupIntervalHours()) {
                        Pokecobbleclaim.LOGGER.info("Creating scheduled player data backup");
                        String backupPath = PlayerDataUtils.backupAllPlayerData();

                        if (backupPath != null) {
                            Pokecobbleclaim.LOGGER.info("Backup created at: " + backupPath);
                            lastBackupTime = now;

                            // Clean up old backups
                            int deleted = PlayerDataUtils.cleanupOldBackups(BackupConfig.getMaxBackups());
                            if (deleted > 0) {
                                Pokecobbleclaim.LOGGER.info("Cleaned up " + deleted + " old backups");
                            }
                        } else {
                            Pokecobbleclaim.LOGGER.warn("Failed to create scheduled backup");
                        }
                    }
                }
            }

            // Update towns (elections, etc.) every second (20 ticks)
            if (s.getTicks() % 20 == 0 && !isShuttingDown) {
                TownManager.getInstance().update();
            }

            // Check for food price updates every minute (1200 ticks)
            if (s.getTicks() % 1200 == 0 && !isShuttingDown) {
                com.pokecobble.phone.food.FoodPriceTimer.getInstance().onServerTick(s);
            }

            // Perform town request maintenance tasks
            if (!isShuttingDown) {
                com.pokecobble.town.TownRequestManager.getInstance().performPeriodicMaintenance(s.getTicks());
            }

            // Perform claim data maintenance (auto-save, cleanup, backup) every 10 minutes (12000 ticks)
            if (s.getTicks() % 12000 == 0 && !isShuttingDown) {
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance().performMaintenance();
                com.pokecobble.town.claim.ClaimDataBackup.performAutoBackup();

                // Perform food price data backup
                com.pokecobble.phone.food.data.FoodPriceStorage.performAutoBackup();
            }

            // Check watching players' balances every 5 seconds (100 ticks)
            // The internal check in MoneyNetworkHandler will limit the actual check frequency
            // and handle errors gracefully to prevent lag and rate limiting
            if (s.getTicks() % 100 == 0) {
                com.pokecobble.town.network.money.MoneyNetworkHandler.checkWatchingPlayersBalances(s);
            }
        });
    }
}
