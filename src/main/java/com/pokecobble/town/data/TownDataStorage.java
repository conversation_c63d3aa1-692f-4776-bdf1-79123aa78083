package com.pokecobble.town.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Handles saving and loading town data to/from disk.
 * Each town is saved to its own individual file in the towns/ subdirectory.
 */
public class TownDataStorage {
    // Constants for file paths
    private static final String DATA_FOLDER = "pokecobbleclaim";
    private static final String TOWNS_FOLDER = "towns";
    private static final String TOWNS_FILE = "towns.json"; // Legacy file for migration
    private static final String TOWN_FILE_EXTENSION = ".json";

    // GSON instance for serialization
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    /**
     * Saves all towns to individual files.
     */
    public static void saveTowns() {
        try {
            // Get all towns from the TownManager
            Collection<Town> towns = TownManager.getInstance().getAllTowns();

            // Create towns directory if it doesn't exist
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                townsDir.mkdirs();
            }

            int savedCount = 0;
            for (Town town : towns) {
                try {
                    saveTown(town);
                    savedCount++;
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Failed to save individual town '" + town.getName() + "': " + e.getMessage());
                }
            }

            Pokecobbleclaim.LOGGER.info("Saved " + savedCount + " towns to individual files");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save towns: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Loads all towns from individual files.
     */
    public static void loadTowns() {
        // Use the new individual file loading method
        loadTownsFromIndividualFiles();

        // Log final state after loading
        int finalTownCount = TownManager.getInstance().getAllTowns().size();
        Pokecobbleclaim.LOGGER.info("Town data loading completed. Final town count in TownManager: " + finalTownCount);
    }

    /**
     * Saves a single town to its own file.
     *
     * @param town The town to save
     */
    public static void saveTown(Town town) {
        try {
            // Create towns directory if it doesn't exist
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                townsDir.mkdirs();
            }

            // Convert town to serializable format
            SerializableTown serializableTown = new SerializableTown(town);

            // Create town file
            File townFile = new File(townsDir, town.getId().toString() + TOWN_FILE_EXTENSION);

            // Use atomic file operations to prevent data corruption
            File tempFile = File.createTempFile("town_", ".tmp", townsDir);

            try (FileWriter writer = new FileWriter(tempFile)) {
                GSON.toJson(serializableTown, writer);
            }

            // Atomic move from temp file to final file
            if (!tempFile.renameTo(townFile)) {
                // If rename fails, try copy and delete
                try (FileReader reader = new FileReader(tempFile);
                     FileWriter writer = new FileWriter(townFile)) {
                    char[] buffer = new char[8192];
                    int length;
                    while ((length = reader.read(buffer)) != -1) {
                        writer.write(buffer, 0, length);
                    }
                }
                tempFile.delete();
            }

            Pokecobbleclaim.LOGGER.info("Saved town '" + town.getName() + "' to individual file: " + townFile.getName());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save town '" + town.getName() + "': " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Loads a single town from its file.
     *
     * @param townId The UUID of the town to load
     * @return The loaded town, or null if not found
     */
    public static Town loadTown(UUID townId) {
        try {
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                return null;
            }

            File townFile = new File(townsDir, townId.toString() + TOWN_FILE_EXTENSION);
            if (!townFile.exists()) {
                return null;
            }

            try (FileReader reader = new FileReader(townFile)) {
                SerializableTown serializableTown = GSON.fromJson(reader, SerializableTown.class);
                if (serializableTown != null) {
                    Town town = serializableTown.toTown();
                    Pokecobbleclaim.LOGGER.debug("Loaded town '" + town.getName() + "' from individual file");
                    return town;
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load town " + townId + ": " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Loads all towns from individual files.
     */
    public static void loadTownsFromIndividualFiles() {
        try {
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                Pokecobbleclaim.LOGGER.info("No towns directory found, creating new directory");
                townsDir.mkdirs();
                return;
            }

            File[] townFiles = townsDir.listFiles((dir, name) -> name.endsWith(TOWN_FILE_EXTENSION));
            if (townFiles == null || townFiles.length == 0) {
                Pokecobbleclaim.LOGGER.info("No individual town files found");
                return;
            }

            Pokecobbleclaim.LOGGER.info("Found " + townFiles.length + " individual town files, loading...");

            // Clear existing towns but keep player-town mappings
            TownManager.getInstance().clearTownsKeepMappings();

            int totalPlayersRestored = 0;
            int loadedTowns = 0;

            for (File townFile : townFiles) {
                try (FileReader reader = new FileReader(townFile)) {
                    SerializableTown serializableTown = GSON.fromJson(reader, SerializableTown.class);
                    if (serializableTown != null) {
                        Town town = serializableTown.toTown();

                        // Add town without immediate synchronization during loading
                        TownManager.getInstance().addTown(town, false);

                        // Update player-town mappings for all players in this town
                        for (UUID playerId : town.getPlayers()) {
                            TownManager.getInstance().updatePlayerTownMapping(playerId, town.getId());
                            totalPlayersRestored++;
                        }

                        // Mark town as having changed player and rank data to ensure synchronization after server restart
                        town.markChanged(Town.ASPECT_PLAYERS);
                        town.markChanged(Town.ASPECT_RANKS);
                        town.markChanged(Town.ASPECT_PERMISSIONS);

                        loadedTowns++;
                        Pokecobbleclaim.LOGGER.info("Loaded town '" + town.getName() + "' with " + town.getPlayerCount() + " players from " + townFile.getName());
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Failed to load town from file " + townFile.getName() + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }

            Pokecobbleclaim.LOGGER.info("Restored " + totalPlayersRestored + " player-town relationships");
            Pokecobbleclaim.LOGGER.info("Loaded " + loadedTowns + " towns from individual files");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load towns from individual files: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Deletes a town's individual file.
     *
     * @param townId The UUID of the town to delete
     * @return true if the file was deleted successfully, false otherwise
     */
    public static boolean deleteTown(UUID townId) {
        try {
            File townsDir = getTownsDirectory();
            if (!townsDir.exists()) {
                return false;
            }

            File townFile = new File(townsDir, townId.toString() + TOWN_FILE_EXTENSION);
            if (townFile.exists()) {
                boolean deleted = townFile.delete();
                if (deleted) {
                    Pokecobbleclaim.LOGGER.info("Deleted town file: " + townFile.getName());
                } else {
                    Pokecobbleclaim.LOGGER.warn("Failed to delete town file: " + townFile.getName());
                }
                return deleted;
            }
            return true; // File doesn't exist, consider it "deleted"
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to delete town file for " + townId + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Gets the towns directory, creating it if it doesn't exist.
     *
     * @return The towns directory
     */
    private static File getTownsDirectory() {
        File dataDir = new File(FabricLoader.getInstance().getGameDir().toFile(), DATA_FOLDER);
        if (!dataDir.exists()) {
            dataDir.mkdirs();
        }
        return new File(dataDir, TOWNS_FOLDER);
    }

    /**
     * A serializable version of the Town class.
     */
    private static class SerializableTown {
        private String name;
        private List<String> playerIds;
        private Map<String, String> playerNames; // Map of UUID -> player name for persistence
        private String id;
        private String description;
        private boolean isOpen;
        private int maxPlayers;
        private String mayorId;
        private boolean inElection;
        private int claimCount;
        private Map<String, Map<String, Boolean>> permissions;
        private Map<String, String> playerRanks;
        private String image; // Town image name
        private long creationDate; // Creation date as timestamp
        private Map<String, Object> townSettings; // Town settings from TownSettingsManager
        private List<SerializableClaimTag> claimTags; // Claim tags for the town
        private List<SerializableTownBan> bannedPlayers; // Banned players for the town
        private List<SerializableTownJob> townJobs; // Town jobs
        private SerializableTownBank townBank; // Town bank data
        private List<String> approvedBuildings; // Approved buildings for the town
        // Claim history is now handled by SimpleClaimHistoryManager - no serialization needed

        public SerializableTown() {
            // Default constructor for GSON
        }

        public SerializableTown(Town town) {
            this.name = town.getName();
            this.playerIds = new ArrayList<>();
            this.playerNames = new HashMap<>();

            // Save both player IDs and names for better persistence
            for (UUID playerId : town.getPlayers()) {
                String playerIdStr = playerId.toString();
                this.playerIds.add(playerIdStr);

                // Get player name from TownPlayer object
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer != null && townPlayer.getName() != null && !townPlayer.getName().isEmpty()) {
                    this.playerNames.put(playerIdStr, townPlayer.getName());
                } else {
                    // Fallback: try to get name from server if available
                    this.playerNames.put(playerIdStr, "Unknown Player");
                }
            }

            this.id = town.getId().toString();
            this.description = town.getDescription();
            this.isOpen = town.isOpen();
            this.maxPlayers = town.getMaxPlayers();

            // Save mayor ID (first player in the list is the mayor/owner)
            if (!town.getPlayers().isEmpty()) {
                UUID mayorId = town.getPlayers().get(0);
                this.mayorId = mayorId.toString();
            }

            this.inElection = town.isInElection();
            this.claimCount = town.getClaimCount();

            // Save permissions (town doesn't have permissions directly, only players do)
            this.permissions = new HashMap<>();

            // Save player ranks
            this.playerRanks = new HashMap<>();
            for (UUID playerId : town.getPlayers()) {
                TownPlayerRank rank = town.getPlayerRank(playerId);
                if (rank != null) {
                    this.playerRanks.put(playerId.toString(), rank.name());
                }
            }

            // Save town image
            this.image = town.getImage();

            // Save creation date
            if (town.getCreationDate() != null) {
                this.creationDate = town.getCreationDate().getTime();
            } else {
                // Fallback for existing towns without creation date
                this.creationDate = System.currentTimeMillis();
            }

            // Save town settings from TownSettingsManager
            try {
                // Only save settings if they exist in the manager (not defaults)
                Map<String, Object> existingSettings = com.pokecobble.town.config.TownSettingsManager.getExistingTownSettings(town.getId());
                if (existingSettings != null && !existingSettings.isEmpty()) {
                    this.townSettings = new HashMap<>(existingSettings);
                    Pokecobbleclaim.LOGGER.debug("Saved town settings for town " + town.getName() + ": " + this.townSettings);
                } else {
                    this.townSettings = null; // No custom settings to save
                    Pokecobbleclaim.LOGGER.debug("No custom town settings to save for town " + town.getName());
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to save town settings for town " + town.getName() + ": " + e.getMessage());
                this.townSettings = null;
            }

            // Save claim tags
            this.claimTags = new ArrayList<>();
            List<com.pokecobble.town.claim.ClaimTag> townClaimTags = town.getClaimTags();
            if (townClaimTags != null && !townClaimTags.isEmpty()) {
                for (com.pokecobble.town.claim.ClaimTag tag : townClaimTags) {
                    this.claimTags.add(new SerializableClaimTag(tag));
                }
                Pokecobbleclaim.LOGGER.debug("Saved {} claim tags for town {}", this.claimTags.size(), town.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("No claim tags to save for town {}", town.getName());
            }

            // Save banned players
            this.bannedPlayers = new ArrayList<>();
            List<com.pokecobble.town.TownBan> townBans = town.getAllBans();
            if (townBans != null && !townBans.isEmpty()) {
                for (com.pokecobble.town.TownBan ban : townBans) {
                    this.bannedPlayers.add(new SerializableTownBan(ban));
                }
                Pokecobbleclaim.LOGGER.debug("Saved {} bans for town {}", this.bannedPlayers.size(), town.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("No bans to save for town {}", town.getName());
            }

            // Save town jobs
            this.townJobs = new ArrayList<>();
            Map<com.pokecobble.town.TownJob.JobType, com.pokecobble.town.TownJob> jobs = town.getTownJobs();
            if (jobs != null && !jobs.isEmpty()) {
                for (com.pokecobble.town.TownJob job : jobs.values()) {
                    this.townJobs.add(new SerializableTownJob(job));
                }
                Pokecobbleclaim.LOGGER.debug("Saved {} jobs for town {}", this.townJobs.size(), town.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("No jobs to save for town {}", town.getName());
            }

            // Save town bank data
            com.pokecobble.town.bank.TownBank townBank = com.pokecobble.town.bank.TownBankManager.getInstance().getTownBank(town.getId());
            if (townBank != null) {
                this.townBank = new SerializableTownBank(townBank);
                Pokecobbleclaim.LOGGER.debug("Saved bank data for town {}", town.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("No bank data to save for town {}", town.getName());
            }

            // Save approved buildings
            this.approvedBuildings = new ArrayList<>();
            for (com.pokecobble.town.TownRequest.RequestType buildingType : town.getApprovedBuildings()) {
                this.approvedBuildings.add(buildingType.name());
            }

            // Claim history is now handled by SimpleClaimHistoryManager - no saving needed
        }

        public Town toTown() {
            Town town = new Town(name);
            // Set ID
            try {
                UUID townId = UUID.fromString(id);
                town.setId(townId);
            } catch (IllegalArgumentException e) {
                Pokecobbleclaim.LOGGER.warn("Invalid town ID: " + id + ", generating new ID");
            }

            // Set other properties
            town.setDescription(description);
            town.setOpen(isOpen);
            town.setMaxPlayers(maxPlayers);

            // Add players with proper TownPlayer objects
            for (String playerIdStr : playerIds) {
                try {
                    UUID playerId = UUID.fromString(playerIdStr);

                    // Get player name - prioritize stored name, fallback to PlayerDataStorage
                    String playerName = "Unknown Player";
                    if (playerNames != null && playerNames.containsKey(playerIdStr)) {
                        playerName = playerNames.get(playerIdStr);
                    } else {
                        // Fallback: try to load player data from disk to get the name
                        try {
                            PlayerDataStorage.SerializablePlayerData playerData =
                                PlayerDataStorage.loadPlayerData(playerId);
                            if (playerData != null && playerData.getPlayerName() != null && !playerData.getPlayerName().isEmpty()) {
                                playerName = playerData.getPlayerName();
                            }
                        } catch (Exception e) {
                            Pokecobbleclaim.LOGGER.debug("Could not load player data for " + playerId + ", using default name");
                        }
                    }

                    // Get player rank from saved data
                    TownPlayerRank playerRank = TownPlayerRank.MEMBER;
                    if (playerRanks != null && playerRanks.containsKey(playerIdStr)) {
                        try {
                            playerRank = TownPlayerRank.valueOf(playerRanks.get(playerIdStr));

                            // Data migration: Fix any VISITOR ranks for town members
                            if (playerRank == TownPlayerRank.VISITOR) {
                                Pokecobbleclaim.LOGGER.warn("Found VISITOR rank for town member " + playerIdStr + " in town " + name + ", correcting to MEMBER");
                                playerRank = TownPlayerRank.MEMBER;
                                // Update the saved data to fix this permanently
                                playerRanks.put(playerIdStr, playerRank.name());
                            }
                        } catch (IllegalArgumentException e) {
                            Pokecobbleclaim.LOGGER.warn("Invalid player rank for " + playerIdStr + ": " + playerRanks.get(playerIdStr));
                        }
                    }

                    // PRIORITY 1: Try to load from file-based permission storage (new system)
                    Map<String, Map<String, Boolean>> filePermissions =
                        com.pokecobble.town.permission.PermissionManager.getInstance().getPlayerPermissions(playerId, town.getId());
                    TownPlayer townPlayer;

                    if (filePermissions != null && !filePermissions.isEmpty()) {
                        // Create TownPlayer with file-based permissions (new system)
                        townPlayer = TownPlayer.fromSavedData(playerId, playerName, playerRank, filePermissions);
                        townPlayer.setOnline(false);
                        Pokecobbleclaim.LOGGER.info("Loaded player " + playerName + " with permissions from file storage (rank: " +
                            playerRank.getDisplayName() + ") in town " + name);

                        // Log some permission details for debugging
                        if (filePermissions.containsKey("Claim Tool")) {
                            Boolean canAccessClaimTool = filePermissions.get("Claim Tool").get("Can access claim tool");
                            Pokecobbleclaim.LOGGER.debug("Player " + playerName + " claim tool access from file storage: " + canAccessClaimTool);
                        }
                    } else {
                        // PRIORITY 2: Try to load individual player permission data from old system
                        PlayerDataStorage.SerializablePlayerData savedPlayerData = PlayerDataStorage.loadPlayerData(playerId);

                        if (savedPlayerData != null) {
                            // Use saved player data with custom permissions (old system)
                            townPlayer = savedPlayerData.toTownPlayer();
                            townPlayer.setOnline(false); // Player is offline during server startup
                            Pokecobbleclaim.LOGGER.info("Loaded saved permission data for player " + playerName + " from old system (rank: " +
                                townPlayer.getRank().getDisplayName() + ") in town " + name);

                            // Log some permission details for debugging
                            Map<String, Map<String, Boolean>> permissions = townPlayer.getAllPermissions();
                            if (permissions.containsKey("Claim Tool")) {
                                Boolean canAccessClaimTool = permissions.get("Claim Tool").get("Can access claim tool");
                                Pokecobbleclaim.LOGGER.debug("Player " + playerName + " claim tool access from old system: " + canAccessClaimTool);
                            }
                        } else {
                            // PRIORITY 3: Create TownPlayer object with default permissions (no saved data available)
                            townPlayer = new TownPlayer(playerId, playerName, playerRank);
                            townPlayer.setOnline(false); // Player is offline during server startup
                            Pokecobbleclaim.LOGGER.info("Created default permission data for player " + playerName + " (rank: " +
                                playerRank.getDisplayName() + ") in town " + name);
                        }
                    }

                    // Add the TownPlayer to the town using the proper method
                    town.addPlayer(townPlayer);

                    // Also set the player rank explicitly to ensure it's properly stored
                    town.setPlayerRank(playerId, playerRank);

                    // Update the player-town mapping in TownManager
                    TownManager.getInstance().updatePlayerTownMapping(playerId, town.getId());

                    Pokecobbleclaim.LOGGER.debug("Restored player " + playerName + " (" + playerId + ") with rank " + playerRank + " to town " + name);
                } catch (IllegalArgumentException e) {
                    Pokecobbleclaim.LOGGER.warn("Invalid player ID: " + playerIdStr + ", skipping");
                }
            }

            // Mayor rank is now handled by the saved player ranks data
            // No automatic rank assignment during loading

            // Set election status
            town.setInElection(inElection);

            // Set claim count
            town.setClaimCount(claimCount);

            // Set permissions (town doesn't have permissions directly, only players do)
            // We'll skip this for now

            // Set player ranks
            if (playerRanks != null) {
                for (Map.Entry<String, String> entry : playerRanks.entrySet()) {
                    try {
                        UUID playerId = UUID.fromString(entry.getKey());
                        TownPlayerRank rank = TownPlayerRank.valueOf(entry.getValue());
                        town.setPlayerRank(playerId, rank);
                    } catch (IllegalArgumentException e) {
                        Pokecobbleclaim.LOGGER.warn("Invalid player ID or rank: " + entry.getKey() + " -> " + entry.getValue());
                    }
                }
            }

            // Set town image
            if (image != null) {
                town.setImage(image);
            }

            // Set creation date
            if (creationDate > 0) {
                town.setCreationDate(new java.util.Date(creationDate));
            } else {
                // Fallback for existing towns without creation date
                town.setCreationDate(new java.util.Date(System.currentTimeMillis()));
            }

            // Restore town settings to TownSettingsManager
            if (townSettings != null && !townSettings.isEmpty()) {
                try {
                    com.pokecobble.town.config.TownSettingsManager.setTownSettings(town.getId(), townSettings);
                    Pokecobbleclaim.LOGGER.info("Restored town settings for town " + town.getName() + ": " + townSettings);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to restore town settings for town " + town.getName() + ": " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                Pokecobbleclaim.LOGGER.info("No town settings to restore for town " + town.getName() + ", will use defaults");
            }

            // Apply persisted settings to the town object to ensure it reflects the correct state
            // This is critical because the serialized town data might be outdated compared to the persisted settings
            try {
                com.pokecobble.town.config.TownSettingsManager.applyPersistedSettingsToTown(town);
                Pokecobbleclaim.LOGGER.debug("Applied persisted settings to town object for " + town.getName());
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to apply persisted settings to town object for " + town.getName() + ": " + e.getMessage());
            }

            // Load claim tags
            if (claimTags != null && !claimTags.isEmpty()) {
                List<com.pokecobble.town.claim.ClaimTag> townClaimTags = new ArrayList<>();
                for (SerializableClaimTag serializableTag : claimTags) {
                    try {
                        com.pokecobble.town.claim.ClaimTag tag = serializableTag.toClaimTag();
                        townClaimTags.add(tag);
                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.warn("Failed to load claim tag for town " + town.getName() + ": " + e.getMessage());
                    }
                }
                town.updateClaimTags(townClaimTags);
                Pokecobbleclaim.LOGGER.debug("Loaded {} claim tags for town {}", townClaimTags.size(), town.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("No claim tags to load for town {}", town.getName());
            }

            // Load banned players
            if (bannedPlayers != null && !bannedPlayers.isEmpty()) {
                int loadedBans = 0;
                for (SerializableTownBan serializableBan : bannedPlayers) {
                    try {
                        com.pokecobble.town.TownBan ban = serializableBan.toTownBan();
                        if (ban != null) {
                            // Directly add the ban to preserve the original ban date
                            town.addBanDirect(ban);
                            loadedBans++;
                        }
                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.warn("Failed to load ban for town " + town.getName() + ": " + e.getMessage());
                    }
                }
                Pokecobbleclaim.LOGGER.debug("Loaded {} bans for town {}", loadedBans, town.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("No bans to load for town {}", town.getName());
            }

            // Load town jobs
            if (townJobs != null && !townJobs.isEmpty()) {
                int loadedJobs = 0;
                for (SerializableTownJob serializableJob : townJobs) {
                    try {
                        com.pokecobble.town.TownJob job = serializableJob.toTownJob();
                        if (job != null) {
                            town.setTownJob(job);
                            loadedJobs++;
                        }
                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.warn("Failed to load job for town " + town.getName() + ": " + e.getMessage());
                    }
                }
                Pokecobbleclaim.LOGGER.debug("Loaded {} jobs for town {}", loadedJobs, town.getName());
            } else {
                Pokecobbleclaim.LOGGER.debug("No jobs to load for town {}, initializing with default unemployed job", town.getName());
                // Ensure unemployed job exists for towns loaded from old data
                if (town.getTownJob(com.pokecobble.town.TownJob.JobType.UNEMPLOYED) == null) {
                    town.setTownJob(new com.pokecobble.town.TownJob(com.pokecobble.town.TownJob.JobType.UNEMPLOYED));
                }
            }

            // Load town bank data
            if (townBank != null) {
                try {
                    com.pokecobble.town.bank.TownBank bank = townBank.toTownBank();
                    com.pokecobble.town.bank.TownBankManager.getInstance().loadTownBank(bank);
                    Pokecobbleclaim.LOGGER.debug("Loaded bank data for town {}", town.getName());
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to load bank data for town " + town.getName() + ": " + e.getMessage());
                    // Create a new bank if loading fails
                    com.pokecobble.town.bank.TownBankManager.getInstance().createTownBank(town.getId());
                }
            } else {
                Pokecobbleclaim.LOGGER.debug("No bank data to load for town {}, creating new bank", town.getName());
                // Create a new bank for towns without bank data
                com.pokecobble.town.bank.TownBankManager.getInstance().createTownBank(town.getId());
            }

            // Load town bank data
            if (townBank != null) {
                try {
                    com.pokecobble.town.bank.TownBank bank = townBank.toTownBank();
                    com.pokecobble.town.bank.TownBankManager.getInstance().loadTownBank(bank);
                    Pokecobbleclaim.LOGGER.debug("Loaded bank data for town {}", town.getName());
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to load bank data for town " + town.getName() + ": " + e.getMessage());
                    // Create a new bank if loading fails
                    com.pokecobble.town.bank.TownBankManager.getInstance().createTownBank(town.getId());
                }
            } else {
                Pokecobbleclaim.LOGGER.debug("No bank data to load for town {}, creating new bank", town.getName());
                // Create a new bank for towns without bank data
                com.pokecobble.town.bank.TownBankManager.getInstance().createTownBank(town.getId());
            }

            // Load approved buildings
            if (approvedBuildings != null) {
                for (String buildingName : approvedBuildings) {
                    try {
                        com.pokecobble.town.TownRequest.RequestType buildingType =
                            com.pokecobble.town.TownRequest.RequestType.valueOf(buildingName);
                        town.approveBuilding(buildingType);
                    } catch (IllegalArgumentException e) {
                        Pokecobbleclaim.LOGGER.warn("Unknown building type '{}' for town {}", buildingName, town.getName());
                    }
                }
            }

            // Claim history is now handled by SimpleClaimHistoryManager - no loading needed

            return town;
        }
    }

    /**
     * A serializable version of the ClaimTag class.
     */
    private static class SerializableClaimTag {
        private String id;
        private String name;
        private String description;
        private int color;

        // Permission settings - using simple boolean flags for each rank and permission type
        private Map<String, Map<String, Boolean>> rankPermissions;

        public SerializableClaimTag() {
            // Default constructor for GSON
        }

        public SerializableClaimTag(com.pokecobble.town.claim.ClaimTag tag) {
            this.id = tag.getId().toString();
            this.name = tag.getName();
            this.description = tag.getDescription();
            this.color = tag.getColor();

            // Serialize rank permissions
            this.rankPermissions = new HashMap<>();

            // Get the rank permissions object from the tag
            com.pokecobble.town.claim.RankPermissions rankPerms = tag.getRankPermissions();

            // Save permissions for each rank
            for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                Map<String, Boolean> permissions = new HashMap<>();

                permissions.put("build", rankPerms.hasPermission(rank, 0));
                permissions.put("interact", rankPerms.hasPermission(rank, 1));
                permissions.put("containers", rankPerms.hasPermission(rank, 2));
                permissions.put("redstone", rankPerms.hasPermission(rank, 3));
                permissions.put("doors", rankPerms.hasPermission(rank, 4));
                permissions.put("crops", rankPerms.hasPermission(rank, 5));
                permissions.put("animals", rankPerms.hasPermission(rank, 6));
                permissions.put("villagers", rankPerms.hasPermission(rank, 7));

                this.rankPermissions.put(rank.name(), permissions);
            }

            // Also save permissions for non-members (null rank)
            Map<String, Boolean> nonMemberPermissions = new HashMap<>();
            nonMemberPermissions.put("build", rankPerms.hasPermission(null, 0));
            nonMemberPermissions.put("interact", rankPerms.hasPermission(null, 1));
            nonMemberPermissions.put("containers", rankPerms.hasPermission(null, 2));
            nonMemberPermissions.put("redstone", rankPerms.hasPermission(null, 3));
            nonMemberPermissions.put("doors", rankPerms.hasPermission(null, 4));
            nonMemberPermissions.put("crops", rankPerms.hasPermission(null, 5));
            nonMemberPermissions.put("animals", rankPerms.hasPermission(null, 6));
            nonMemberPermissions.put("villagers", rankPerms.hasPermission(null, 7));
            this.rankPermissions.put("NON_MEMBER", nonMemberPermissions);
        }

        public com.pokecobble.town.claim.ClaimTag toClaimTag() {
            // FIXED: Preserve UUID when deserializing from storage
            com.pokecobble.town.claim.ClaimTag tag;
            if (id != null && !id.isEmpty()) {
                try {
                    java.util.UUID uuid = java.util.UUID.fromString(id);
                    tag = new com.pokecobble.town.claim.ClaimTag(uuid, name, description, color);
                } catch (IllegalArgumentException e) {
                    // If UUID parsing fails, create with new UUID but log warning
                    com.pokecobble.Pokecobbleclaim.LOGGER.warn("Failed to parse stored tag UUID '{}', creating new UUID", id);
                    tag = new com.pokecobble.town.claim.ClaimTag(name, description, color);
                }
            } else {
                // No ID stored, create with new UUID
                tag = new com.pokecobble.town.claim.ClaimTag(name, description, color);
            }

            // Restore permissions for each rank
            if (rankPermissions != null) {
                for (Map.Entry<String, Map<String, Boolean>> rankEntry : rankPermissions.entrySet()) {
                    String rankName = rankEntry.getKey();
                    Map<String, Boolean> permissions = rankEntry.getValue();

                    com.pokecobble.town.TownPlayerRank rank = null;
                    if (!"NON_MEMBER".equals(rankName)) {
                        try {
                            rank = com.pokecobble.town.TownPlayerRank.valueOf(rankName);
                        } catch (IllegalArgumentException e) {
                            // Skip invalid rank names
                            continue;
                        }
                    }

                    // Set permissions for this rank using the correct method
                    if (permissions != null) {
                        tag.setPermissionForRank(rank, 0, permissions.getOrDefault("build", false));
                        tag.setPermissionForRank(rank, 1, permissions.getOrDefault("interact", false));
                        tag.setPermissionForRank(rank, 2, permissions.getOrDefault("containers", false));
                        tag.setPermissionForRank(rank, 3, permissions.getOrDefault("redstone", false));
                        tag.setPermissionForRank(rank, 4, permissions.getOrDefault("doors", false));
                        tag.setPermissionForRank(rank, 5, permissions.getOrDefault("crops", false));
                        tag.setPermissionForRank(rank, 6, permissions.getOrDefault("animals", false));
                        tag.setPermissionForRank(rank, 7, permissions.getOrDefault("villagers", false));
                    }
                }
            }

            return tag;
        }
    }

    /**
     * A serializable version of the TownBan class.
     */
    private static class SerializableTownBan {
        private String bannedPlayerId;
        private String bannedPlayerName;
        private String bannedByPlayerId;
        private String bannedByPlayerName;
        private long banDate;
        private String reason;
        private Long expirationDate; // null for permanent bans

        public SerializableTownBan() {
            // Default constructor for GSON
        }

        public SerializableTownBan(com.pokecobble.town.TownBan ban) {
            this.bannedPlayerId = ban.getBannedPlayerId().toString();
            this.bannedPlayerName = ban.getBannedPlayerName();
            this.bannedByPlayerId = ban.getBannedByPlayerId().toString();
            this.bannedByPlayerName = ban.getBannedByPlayerName();
            this.banDate = ban.getBanDate().getTime();
            this.reason = ban.getReason();
            this.expirationDate = ban.getExpirationDate() != null ? ban.getExpirationDate().getTime() : null;
        }

        public com.pokecobble.town.TownBan toTownBan() {
            try {
                UUID bannedPlayerUUID = UUID.fromString(bannedPlayerId);
                UUID bannedByPlayerUUID = UUID.fromString(bannedByPlayerId);
                java.util.Date banDateObj = new java.util.Date(banDate);
                java.util.Date expirationDateObj = expirationDate != null ? new java.util.Date(expirationDate) : null;

                // Create TownBan using the constructor that accepts ban date for proper deserialization
                return new com.pokecobble.town.TownBan(bannedPlayerUUID, bannedPlayerName,
                                                     bannedByPlayerUUID, bannedByPlayerName,
                                                     reason, banDateObj, expirationDateObj);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to deserialize town ban: " + e.getMessage());
                return null;
            }
        }
    }

    /**
     * A serializable version of the TownJob class.
     */
    private static class SerializableTownJob {
        private String jobType;
        private String approvalStatus;
        private long unlockRequestDate;
        private Long approvalDate; // null if not approved/rejected yet
        private String requestedByPlayer; // UUID as string
        private String adminNotes;
        private boolean isUnlocked;

        public SerializableTownJob() {
            // Default constructor for GSON
        }

        public SerializableTownJob(com.pokecobble.town.TownJob job) {
            this.jobType = job.getJobType().name();
            this.approvalStatus = job.getApprovalStatus().name();
            this.unlockRequestDate = job.getUnlockRequestDate().getTime();
            this.approvalDate = job.getApprovalDate() != null ? job.getApprovalDate().getTime() : null;
            this.requestedByPlayer = job.getRequestedByPlayer() != null ? job.getRequestedByPlayer().toString() : null;
            this.adminNotes = job.getAdminNotes();
            this.isUnlocked = job.isUnlocked();
        }

        public com.pokecobble.town.TownJob toTownJob() {
            try {
                com.pokecobble.town.TownJob.JobType jobTypeEnum = com.pokecobble.town.TownJob.JobType.valueOf(jobType);
                com.pokecobble.town.TownJob.ApprovalStatus approvalStatusEnum = com.pokecobble.town.TownJob.ApprovalStatus.valueOf(approvalStatus);
                java.util.Date unlockRequestDateObj = new java.util.Date(unlockRequestDate);
                java.util.Date approvalDateObj = approvalDate != null ? new java.util.Date(approvalDate) : null;
                UUID requestedByPlayerUUID = requestedByPlayer != null ? UUID.fromString(requestedByPlayer) : null;

                return new com.pokecobble.town.TownJob(jobTypeEnum, approvalStatusEnum, unlockRequestDateObj,
                                                     approvalDateObj, requestedByPlayerUUID, adminNotes, isUnlocked);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to deserialize town job: " + e.getMessage());
                return null;
            }
        }
    }

    // SerializableClaimHistoryEntry removed - claim history now handled by SimpleClaimHistoryManager

    /**
     * A serializable version of the TownBank class.
     */
    private static class SerializableTownBank {
        private String townId;
        private long balance;
        private List<SerializableTownBankTransaction> transactionHistory;
        private long creationDate;
        private Long lastTransactionDate; // null if no transactions yet
        private int dataVersion;
        private boolean allowDeposits;
        private boolean allowWithdrawals;
        private long dailyWithdrawLimit;
        private long maxBalance;

        public SerializableTownBank() {
            // Default constructor for GSON
        }

        public SerializableTownBank(com.pokecobble.town.bank.TownBank bank) {
            this.townId = bank.getTownId().toString();
            this.balance = bank.getBalance();
            this.transactionHistory = new ArrayList<>();
            for (com.pokecobble.town.bank.TownBankTransaction transaction : bank.getTransactionHistory()) {
                this.transactionHistory.add(new SerializableTownBankTransaction(transaction));
            }
            this.creationDate = bank.getCreationDate().getTime();
            this.lastTransactionDate = bank.getLastTransactionDate() != null ?
                bank.getLastTransactionDate().getTime() : null;
            this.dataVersion = bank.getDataVersion();
            this.allowDeposits = bank.isDepositsAllowed();
            this.allowWithdrawals = bank.isWithdrawalsAllowed();
            this.dailyWithdrawLimit = bank.getDailyWithdrawLimit();
            this.maxBalance = bank.getMaxBalance();
        }

        public com.pokecobble.town.bank.TownBank toTownBank() {
            UUID townUuid = UUID.fromString(townId);
            List<com.pokecobble.town.bank.TownBankTransaction> transactions = new ArrayList<>();
            for (SerializableTownBankTransaction serializable : transactionHistory) {
                transactions.add(serializable.toTownBankTransaction());
            }

            return new com.pokecobble.town.bank.TownBank(
                townUuid,
                balance,
                transactions,
                new Date(creationDate),
                lastTransactionDate != null ? new Date(lastTransactionDate) : null,
                dataVersion,
                allowDeposits,
                allowWithdrawals,
                dailyWithdrawLimit,
                maxBalance
            );
        }
    }

    /**
     * A serializable version of the TownBankTransaction class.
     */
    private static class SerializableTownBankTransaction {
        private String type;
        private long amount;
        private String playerId; // null for system transactions
        private String description;
        private long balanceBefore;
        private long balanceAfter;
        private long timestamp;
        private String transactionId;

        public SerializableTownBankTransaction() {
            // Default constructor for GSON
        }

        public SerializableTownBankTransaction(com.pokecobble.town.bank.TownBankTransaction transaction) {
            this.type = transaction.getType().name();
            this.amount = transaction.getAmount();
            this.playerId = transaction.getPlayerId() != null ? transaction.getPlayerId().toString() : null;
            this.description = transaction.getDescription();
            this.balanceBefore = transaction.getBalanceBefore();
            this.balanceAfter = transaction.getBalanceAfter();
            this.timestamp = transaction.getTimestamp();
            this.transactionId = transaction.getTransactionId().toString();
        }

        public com.pokecobble.town.bank.TownBankTransaction toTownBankTransaction() {
            return new com.pokecobble.town.bank.TownBankTransaction(
                com.pokecobble.town.bank.TownBankTransaction.TransactionType.valueOf(type),
                amount,
                playerId != null ? UUID.fromString(playerId) : null,
                description,
                balanceBefore,
                balanceAfter,
                timestamp,
                UUID.fromString(transactionId)
            );
        }
    }
}
