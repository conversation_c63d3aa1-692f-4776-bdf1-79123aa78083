package com.pokecobble.town;


import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.Pokecobbleclaim;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import net.minecraft.util.math.ChunkPos;

/**
 * Represents a town in the game.
 */
public class Town {
    /**
     * Enum representing the different ways players can join a town.
     */
    public enum JoinType {
        OPEN,       // Anyone can join
        CLOSED,     // No one can join
        INVITE_ONLY // Only invited players can join
    }
    private String name;
    private final List<UUID> players;
    private final UUID id;
    private String description;
    private JoinType joinType;
    private int maxPlayers;
    private int claimCount = 0;
    private String image = "default"; // Default town image
    private Date creationDate; // When the town was created

    // Display player count for town list (when full player list isn't available)
    private Integer displayPlayerCount = null;

    // Store TownPlayer objects for each player
    private final Map<UUID, TownPlayer> townPlayers = new HashMap<>();

    // Store player ranks
    private final Map<UUID, TownPlayerRank> playerRanks = new HashMap<>();

    // Store claim tags
    private final List<ClaimTag> claimTags = new ArrayList<>();

    // Claim history is now handled by SimpleClaimHistoryManager

    // Store banned players with ban information
    private final Map<UUID, TownBan> bannedPlayers = new HashMap<>();

    // Store town jobs
    private final Map<TownJob.JobType, TownJob> townJobs = new HashMap<>();

    // Store approved buildings
    private final Set<TownRequest.RequestType> approvedBuildings = new HashSet<>();

    // Version tracking for data synchronization
    private int dataVersion = 0;

    // Track which aspects of the town have changed
    private final Set<String> changedAspects = new HashSet<>();

    // Constants for changed aspects
    public static final String ASPECT_NAME = "name";
    public static final String ASPECT_DESCRIPTION = "description";
    public static final String ASPECT_OPEN = "open";
    public static final String ASPECT_MAX_PLAYERS = "max_players";
    public static final String ASPECT_PLAYERS = "players";
    public static final String ASPECT_RANKS = "ranks";
    public static final String ASPECT_PERMISSIONS = "permissions";
    public static final String ASPECT_CLAIMS = "claims";
    public static final String ASPECT_TAGS = "tags";
    public static final String ASPECT_ELECTION = "election";
    public static final String ASPECT_IMAGE = "image";
    public static final String ASPECT_BANS = "bans";
    public static final String ASPECT_JOBS = "jobs";
    public static final String ASPECT_BUILDINGS = "buildings";

    /**
     * Creates a new town with the given name.
     *
     * @param name The name of the town
     */
    public Town(String name) {
        this.name = name;
        this.players = new ArrayList<>();
        this.id = UUID.randomUUID();
        this.description = "A lovely town";
        this.joinType = JoinType.OPEN;
        this.maxPlayers = 20;
        this.creationDate = new Date(); // Set creation date to current time
        initializeDefaultJobs();
    }

    /**
     * Creates a new town with the given parameters.
     *
     * @param name The name of the town
     * @param description The town description
     * @param joinType The join type for the town
     * @param maxPlayers The maximum number of players allowed
     */
    public Town(String name, String description, JoinType joinType, int maxPlayers) {
        this.name = name;
        this.players = new ArrayList<>();
        this.id = UUID.randomUUID();
        this.description = description;
        this.joinType = joinType;
        this.maxPlayers = maxPlayers;
        this.creationDate = new Date(); // Set creation date to current time
        initializeDefaultJobs();
    }

    /**
     * Creates a new town with the given parameters (legacy constructor).
     *
     * @param name The name of the town
     * @param description The town description
     * @param isOpen Whether the town is open for anyone to join
     * @param maxPlayers The maximum number of players allowed
     */
    public Town(String name, String description, boolean isOpen, int maxPlayers) {
        this.name = name;
        this.players = new ArrayList<>();
        this.id = UUID.randomUUID();
        this.description = description;
        this.joinType = isOpen ? JoinType.OPEN : JoinType.CLOSED;
        this.maxPlayers = maxPlayers;
        this.creationDate = new Date(); // Set creation date to current time
        initializeDefaultJobs();
    }

    /**
     * Creates a new town with the given ID and name.
     *
     * @param id The ID of the town
     * @param name The name of the town
     */
    public Town(UUID id, String name) {
        this.name = name;
        this.players = new ArrayList<>();
        this.id = id;
        this.description = "A lovely town";
        this.joinType = JoinType.OPEN;
        this.maxPlayers = 20;
        this.creationDate = new Date(); // Set creation date to current time
        initializeDefaultJobs();
    }

    /**
     * Gets the name of the town.
     *
     * @return The town name
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the name of the town.
     *
     * @param name The new town name
     */
    public void setName(String name) {
        this.name = name;
        markChanged(ASPECT_NAME);
    }

    /**
     * Gets the unique ID of the town.
     *
     * @return The town ID
     */
    public UUID getId() {
        return id;
    }

    /**
     * Sets the ID of the town.
     * This is used when loading towns from storage.
     *
     * @param id The new town ID
     */
    public void setId(UUID id) {
        // This is a bit of a hack, but we need to be able to set the ID when loading from storage
        // In a real implementation, we would use reflection or a proper serialization system
        try {
            java.lang.reflect.Field idField = Town.class.getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(this, id);
        } catch (Exception e) {
            // If this fails, we'll just keep the existing ID
            System.err.println("Failed to set town ID: " + e.getMessage());
        }
    }

    /**
     * Gets the creation date of the town.
     *
     * @return The creation date
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * Sets the creation date of the town.
     * This is used when loading towns from storage.
     *
     * @param creationDate The creation date
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * Gets a formatted string representation of the creation date.
     *
     * @return A formatted string showing the actual creation date
     */
    public String getFormattedCreationDate() {
        if (creationDate == null) {
            return "Unknown";
        }

        // Format as actual date (e.g., "Dec 25, 2024")
        java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("MMM dd, yyyy");
        return formatter.format(creationDate);
    }

    /**
     * Gets the list of players in the town.
     *
     * @return The list of player UUIDs
     */
    public List<UUID> getPlayers() {
        return players;
    }

    /**
     * Gets the list of player IDs in the town.
     * This is an alias for getPlayers() for compatibility.
     *
     * @return The list of player UUIDs
     */
    public List<UUID> getPlayerIds() {
        return players;
    }

    /**
     * Adds a player to the town.
     *
     * @param playerId The UUID of the player to add
     * @return true if the player was added, false if they were already in the town
     */
    public boolean addPlayer(UUID playerId) {
        if (!players.contains(playerId)) {
            players.add(playerId);

            // No automatic rank assignment - ranks must be set explicitly
            // This prevents interference with manual rank changes
            // Default to MEMBER for display purposes only
            TownPlayerRank rank = TownPlayerRank.MEMBER;

            // Create a TownPlayer object for this player
            TownPlayer player = new TownPlayer(playerId, "Unknown", rank);
            townPlayers.put(playerId, player);

            // Store the rank in our map as a backup
            playerRanks.put(playerId, rank);

            markChanged(ASPECT_PLAYERS);

            return true;
        }
        return false;
    }

    /**
     * Adds a TownPlayer to the town.
     *
     * @param player The TownPlayer to add
     * @return true if the player was added, false if they were already in the town
     */
    public boolean addPlayer(TownPlayer player) {
        UUID playerId = player.getUuid();
        if (!players.contains(playerId)) {
            players.add(playerId);
            townPlayers.put(playerId, player);

            // CRITICAL: Store the player's rank in the authoritative rank map
            // This ensures the rank system works correctly for town creators
            playerRanks.put(playerId, player.getRank());

            markChanged(ASPECT_PLAYERS);
            return true;
        } else if (!townPlayers.containsKey(playerId)) {
            // Player UUID is in the list but not in the map
            townPlayers.put(playerId, player);

            // CRITICAL: Store the player's rank in the authoritative rank map
            playerRanks.put(playerId, player.getRank());

            markChanged(ASPECT_PLAYERS);
            return true;
        } else {
            // Player already exists, update the TownPlayer object
            townPlayers.put(playerId, player);

            // CRITICAL: Update the player's rank in the authoritative rank map
            playerRanks.put(playerId, player.getRank());

            markChanged(ASPECT_PLAYERS);
            return true;
        }
    }

    /**
     * Removes a player from the town.
     *
     * @param playerId The UUID of the player to remove
     * @return true if the player was removed, false if they weren't in the town
     */
    public boolean removePlayer(UUID playerId) {
        townPlayers.remove(playerId);
        boolean removed = players.remove(playerId);
        if (removed) {
            markChanged(ASPECT_PLAYERS);
        }
        return removed;
    }

    /**
     * Clears all players from the town.
     * This is used for client-side updates when receiving new player lists.
     */
    public void clearPlayers() {
        players.clear();
        townPlayers.clear();
        markChanged(ASPECT_PLAYERS);
    }

    /**
     * Bans a player from the town.
     *
     * @param playerId The UUID of the player to ban
     * @param playerName The name of the player to ban
     * @param bannedByPlayerId The UUID of the player issuing the ban
     * @param bannedByPlayerName The name of the player issuing the ban
     * @param reason The reason for the ban
     * @return true if the player was banned, false if they were already banned
     */
    public boolean banPlayer(UUID playerId, String playerName, UUID bannedByPlayerId, String bannedByPlayerName, String reason) {
        if (bannedPlayers.containsKey(playerId)) {
            return false; // Player is already banned
        }

        TownBan ban = new TownBan(playerId, playerName, bannedByPlayerId, bannedByPlayerName, reason);
        bannedPlayers.put(playerId, ban);
        markChanged(ASPECT_BANS);
        return true;
    }

    /**
     * Bans a player from the town with an expiration date.
     *
     * @param playerId The UUID of the player to ban
     * @param playerName The name of the player to ban
     * @param bannedByPlayerId The UUID of the player issuing the ban
     * @param bannedByPlayerName The name of the player issuing the ban
     * @param reason The reason for the ban
     * @param expirationDate When the ban expires (null for permanent)
     * @return true if the player was banned, false if they were already banned
     */
    public boolean banPlayer(UUID playerId, String playerName, UUID bannedByPlayerId, String bannedByPlayerName, String reason, java.util.Date expirationDate) {
        if (bannedPlayers.containsKey(playerId)) {
            return false; // Player is already banned
        }

        TownBan ban = new TownBan(playerId, playerName, bannedByPlayerId, bannedByPlayerName, reason, expirationDate);
        bannedPlayers.put(playerId, ban);
        markChanged(ASPECT_BANS);
        return true;
    }

    /**
     * Unbans a player from the town.
     *
     * @param playerId The UUID of the player to unban
     * @return true if the player was unbanned, false if they weren't banned
     */
    public boolean unbanPlayer(UUID playerId) {
        TownBan removedBan = bannedPlayers.remove(playerId);
        if (removedBan != null) {
            markChanged(ASPECT_BANS);
            return true;
        }
        return false;
    }

    /**
     * Checks if a player is banned from the town.
     *
     * @param playerId The UUID of the player to check
     * @return true if the player is banned and the ban is active, false otherwise
     */
    public boolean isPlayerBanned(UUID playerId) {
        TownBan ban = bannedPlayers.get(playerId);
        if (ban == null) {
            return false;
        }

        // Check if ban has expired
        if (ban.isExpired()) {
            // Automatically remove expired bans
            bannedPlayers.remove(playerId);
            markChanged(ASPECT_BANS);
            return false;
        }

        return true;
    }

    /**
     * Gets the ban information for a player.
     *
     * @param playerId The UUID of the player
     * @return The TownBan object, or null if the player is not banned
     */
    public TownBan getPlayerBan(UUID playerId) {
        TownBan ban = bannedPlayers.get(playerId);
        if (ban != null && ban.isExpired()) {
            // Automatically remove expired bans
            bannedPlayers.remove(playerId);
            markChanged(ASPECT_BANS);
            return null;
        }
        return ban;
    }

    /**
     * Gets all active bans for the town.
     *
     * @return A list of all active TownBan objects
     */
    public java.util.List<TownBan> getActiveBans() {
        java.util.List<TownBan> activeBans = new java.util.ArrayList<>();
        java.util.Iterator<java.util.Map.Entry<UUID, TownBan>> iterator = bannedPlayers.entrySet().iterator();

        while (iterator.hasNext()) {
            java.util.Map.Entry<UUID, TownBan> entry = iterator.next();
            TownBan ban = entry.getValue();

            if (ban.isExpired()) {
                // Remove expired bans
                iterator.remove();
                markChanged(ASPECT_BANS);
            } else {
                activeBans.add(ban);
            }
        }

        return activeBans;
    }

    /**
     * Gets all bans (including expired ones) for the town.
     *
     * @return A list of all TownBan objects
     */
    public java.util.List<TownBan> getAllBans() {
        return new java.util.ArrayList<>(bannedPlayers.values());
    }

    /**
     * Clears all expired bans from the town.
     *
     * @return The number of expired bans that were removed
     */
    public int clearExpiredBans() {
        int removedCount = 0;
        java.util.Iterator<java.util.Map.Entry<UUID, TownBan>> iterator = bannedPlayers.entrySet().iterator();

        while (iterator.hasNext()) {
            java.util.Map.Entry<UUID, TownBan> entry = iterator.next();
            TownBan ban = entry.getValue();

            if (ban.isExpired()) {
                iterator.remove();
                removedCount++;
            }
        }

        if (removedCount > 0) {
            markChanged(ASPECT_BANS);
        }

        return removedCount;
    }

    /**
     * Adds a ban directly to the town (used for data loading).
     * This method preserves the original ban date and doesn't mark as changed.
     *
     * @param ban The TownBan to add
     */
    public void addBanDirect(TownBan ban) {
        if (ban != null) {
            bannedPlayers.put(ban.getBannedPlayerId(), ban);
        }
    }

    /**
     * Gets a player by UUID.
     *
     * @param playerId The UUID of the player
     * @return The TownPlayer object, or null if the player is not in the town
     */
    public TownPlayer getPlayer(UUID playerId) {
        if (!players.contains(playerId)) {
            return null;
        }

        // Check if we have a TownPlayer object for this player
        TownPlayer player = townPlayers.get(playerId);
        if (player == null) {
            // Create a new TownPlayer object with default values
            TownPlayerRank rank = getPlayerRank(playerId);
            player = new TownPlayer(playerId, "Unknown", rank);
            townPlayers.put(playerId, player);
        }

        return player;
    }

    /**
     * Gets the number of players in the town.
     * Uses the display player count if available (for town list display),
     * otherwise returns the actual size of the player list.
     *
     * @return The player count
     */
    public int getPlayerCount() {
        // Use display count if available (for town list display when full player data isn't loaded)
        if (displayPlayerCount != null) {
            return displayPlayerCount;
        }
        // Otherwise use actual player list size
        return players.size();
    }

    /**
     * Sets the display player count for town list display.
     * This is used when the full player list isn't available but we know the count.
     *
     * @param count The player count to display
     */
    public void setDisplayPlayerCount(int count) {
        this.displayPlayerCount = count;
    }

    /**
     * Clears the display player count, causing getPlayerCount() to use the actual player list size.
     */
    public void clearDisplayPlayerCount() {
        this.displayPlayerCount = null;
    }

    /**
     * Gets the town description.
     *
     * @return The town description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the town description.
     *
     * @param description The new description
     */
    public void setDescription(String description) {
        this.description = description;
        markChanged(ASPECT_DESCRIPTION);
    }

    /**
     * Gets the join type of the town.
     *
     * @return The join type
     */
    public JoinType getJoinType() {
        return joinType;
    }

    /**
     * Sets the join type of the town.
     *
     * @param joinType The new join type
     */
    public void setJoinType(JoinType joinType) {
        this.joinType = joinType;
        markChanged(ASPECT_OPEN);
    }

    /**
     * Checks if the town is open for anyone to join.
     * This is a legacy method for backward compatibility.
     *
     * @return true if the town is open, false otherwise
     */
    public boolean isOpen() {
        return joinType == JoinType.OPEN;
    }

    /**
     * Sets whether the town is open for anyone to join.
     * This is a legacy method for backward compatibility.
     *
     * @param open Whether the town is open
     */
    public void setOpen(boolean open) {
        this.joinType = open ? JoinType.OPEN : JoinType.CLOSED;
        markChanged(ASPECT_OPEN);
    }

    /**
     * Gets the maximum number of players allowed in the town.
     *
     * @return The maximum player count
     */
    public int getMaxPlayers() {
        return maxPlayers;
    }

    /**
     * Gets the number of chunks claimed by the town.
     *
     * @return The claim count
     */
    public int getClaimCount() {
        return claimCount;
    }

    /**
     * Sets the number of chunks claimed by the town.
     *
     * @param claimCount The new claim count
     */
    public void setClaimCount(int claimCount) {
        this.claimCount = claimCount;
        markChanged(ASPECT_CLAIMS);
    }

    /**
     * Gets the maximum number of chunks that can be claimed by the town.
     *
     * @return The maximum claim count
     */
    public int getMaxClaims() {
        // Return a fixed number of 4 claims per town
        return 4;
    }

    /**
     * Sets the maximum number of players allowed in the town.
     *
     * @param maxPlayers The maximum player count
     */
    public void setMaxPlayers(int maxPlayers) {
        this.maxPlayers = maxPlayers;
        markChanged(ASPECT_MAX_PLAYERS);
    }

    // Flag to track if we're in an election
    private boolean inElection = false;

    /**
     * Gets the rank of a player in the town.
     * This is the authoritative source for player ranks.
     *
     * @param playerId The UUID of the player
     * @return The player's rank, or null if they're not in the town
     */
    public TownPlayerRank getPlayerRank(UUID playerId) {
        // Check if player is in the town
        if (!players.contains(playerId)) {
            return null; // Player is not in the town
        }

        // AUTHORITATIVE SOURCE: Check stored rank (single source of truth)
        return playerRanks.getOrDefault(playerId, TownPlayerRank.MEMBER);
    }

    /**
     * Sets whether the town is currently in an election.
     *
     * @param inElection Whether the town is in an election
     */
    public void setInElection(boolean inElection) {
        this.inElection = inElection;
        markChanged(ASPECT_ELECTION);
    }

    /**
     * Checks if the town is currently in an election.
     *
     * @return true if the town is in an election, false otherwise
     */
    public boolean isInElection() {
        return inElection;
    }

    /**
     * Sets the rank of a player in the town.
     * This is the authoritative method for changing player ranks.
     *
     * @param playerId The UUID of the player
     * @param rank The new rank for the player
     * @return true if the rank was set, false if the player is not in the town
     */
    public boolean setPlayerRank(UUID playerId, TownPlayerRank rank) {
        // Check if player is in the town
        if (!players.contains(playerId)) {
            return false; // Player is not in the town
        }

        // Get the old rank for comparison
        TownPlayerRank oldRank = playerRanks.get(playerId);

        // Validate rank - town members should not be VISITOR
        if (rank == TownPlayerRank.VISITOR) {
            com.pokecobble.Pokecobbleclaim.LOGGER.warn("Attempted to set VISITOR rank for town member " + playerId + ". Using MEMBER instead.");
            rank = TownPlayerRank.MEMBER;
        }

        // AUTHORITATIVE STORAGE: Store in town's rank map first (single source of truth)
        playerRanks.put(playerId, rank);

        // Sync TownPlayer object if it exists (use internal method to avoid recursion)
        TownPlayer townPlayer = townPlayers.get(playerId);
        if (townPlayer != null) {
            townPlayer.setRankInternal(rank);
        }

        // If setting to OWNER, make sure this player is the first in the list
        if (rank == TownPlayerRank.OWNER) {
            // Remove the player from their current position
            players.remove(playerId);
            // Add them to the front of the list
            players.add(0, playerId);
        }

        // Notify the claim permission system about the rank change
        // This ensures that cached permissions are invalidated and the player
        // gets the correct permissions based on their new rank
        try {
            com.pokecobble.town.claim.v2.ClaimTagIntegration claimIntegration =
                com.pokecobble.town.claim.v2.ClaimTagIntegration.getInstance();
            if (claimIntegration != null) {
                claimIntegration.onPlayerRankChanged(playerId, this.getId(), oldRank, rank);
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error notifying claim system of rank change for player " + playerId + ": " + e.getMessage(), e);
        }

        // Mark town data as changed and trigger sync
        markChanged(ASPECT_RANKS);

        com.pokecobble.Pokecobbleclaim.LOGGER.info("Set rank " + rank.getDisplayName() + " for player " + playerId + " in town " + this.getName());

        return true;
    }





    /**
     * Gets a player's name from their UUID.
     *
     * @param playerId The player's UUID
     * @return The player's name, or "Unknown Player" if not found
     */
    public String getPlayerName(UUID playerId) {
        TownPlayer player = getPlayer(playerId);
        if (player != null) {
            return player.getName();
        }
        return "Unknown Player";
    }

    /**
     * Gets the claimed chunks for this town.
     *
     * @return A collection of claimed chunk positions
     */
    public java.util.Collection<ChunkPos> getClaimedChunks() {
        // This is a stub method that returns an empty list
        // In a real implementation, this would return the actual claimed chunks
        return new java.util.ArrayList<>();
    }

    /**
     * Gets the tag for a specific chunk.
     *
     * @param chunkPos The chunk position
     * @return The tag for the chunk, or null if the chunk has no tag
     */
    public ClaimTag getChunkTag(ChunkPos chunkPos) {
        // This is a stub method that returns null
        // In a real implementation, this would return the actual tag for the chunk
        return null;
    }

    // Claim history methods removed - now handled by SimpleClaimHistoryManager

    /**
     * Gets the claim tags for this town.
     *
     * @return The list of claim tags
     */
    public List<ClaimTag> getClaimTags() {
        return claimTags;
    }

    /**
     * Adds a claim tag to this town.
     *
     * @param tag The tag to add
     */
    public void addClaimTag(ClaimTag tag) {
        if (!claimTags.contains(tag)) {
            claimTags.add(tag);
            markChanged(ASPECT_TAGS);
        }
    }

    /**
     * Removes a claim tag from this town.
     *
     * @param tag The tag to remove
     */
    public void removeClaimTag(ClaimTag tag) {
        boolean removed = claimTags.remove(tag);
        if (removed) {
            markChanged(ASPECT_TAGS);
        }
    }

    /**
     * Updates the claim tags for this town.
     * This is called when tags are modified in the ClaimTagScreen.
     *
     * @param tags The updated list of tags
     */
    public void updateClaimTags(List<ClaimTag> tags) {
        claimTags.clear();
        claimTags.addAll(tags);
        markChanged(ASPECT_TAGS);
    }

    /**
     * Gets the current data version of this town.
     *
     * @return The data version
     */
    public int getDataVersion() {
        return dataVersion;
    }

    /**
     * Sets the data version of this town.
     *
     * @param dataVersion The new data version
     */
    public void setDataVersion(int dataVersion) {
        this.dataVersion = dataVersion;
    }

    /**
     * Increments the data version of this town.
     *
     * @return The new data version
     */
    public int incrementDataVersion() {
        return ++dataVersion;
    }

    /**
     * Marks an aspect of the town as changed.
     *
     * @param aspect The aspect that changed
     */
    public void markChanged(String aspect) {
        changedAspects.add(aspect);
        incrementDataVersion();
    }

    /**
     * Checks if an aspect of the town has changed.
     *
     * @param aspect The aspect to check
     * @return True if the aspect has changed, false otherwise
     */
    public boolean hasChanged(String aspect) {
        return changedAspects.contains(aspect);
    }

    /**
     * Gets all aspects of the town that have changed.
     *
     * @return A set of changed aspects
     */
    public Set<String> getChangedAspects() {
        return new HashSet<>(changedAspects);
    }

    /**
     * Clears all changed aspects.
     * This should be called after synchronizing the town data.
     */
    public void clearChangedAspects() {
        changedAspects.clear();
    }

    /**
     * Gets the image name for this town.
     *
     * @return The image name
     */
    public String getImage() {
        return image;
    }

    /**
     * Sets the image for this town.
     *
     * @param image The new image name
     */
    public void setImage(String image) {
        this.image = image;
        markChanged(ASPECT_IMAGE);
    }

    // ===== JOB MANAGEMENT METHODS =====

    /**
     * Initializes default jobs for a new town (unemployed job only).
     */
    private void initializeDefaultJobs() {
        townJobs.put(TownJob.JobType.UNEMPLOYED, new TownJob(TownJob.JobType.UNEMPLOYED));
    }

    /**
     * Gets all jobs for this town.
     *
     * @return Map of job types to town jobs
     */
    public Map<TownJob.JobType, TownJob> getTownJobs() {
        return new HashMap<>(townJobs);
    }

    /**
     * Gets a specific job for this town.
     *
     * @param jobType The job type to get
     * @return The town job, or null if not found
     */
    public TownJob getTownJob(TownJob.JobType jobType) {
        return townJobs.get(jobType);
    }

    /**
     * Adds or updates a job for this town.
     *
     * @param job The job to add or update
     */
    public void setTownJob(TownJob job) {
        townJobs.put(job.getJobType(), job);
        markChanged(ASPECT_JOBS);
    }

    /**
     * Requests to unlock a job for this town.
     *
     * @param jobType The job type to unlock
     * @param requestedBy The player requesting the unlock
     * @return true if the request was successful, false otherwise
     */
    public boolean requestJobUnlock(TownJob.JobType jobType, UUID requestedBy) {
        if (jobType == TownJob.JobType.UNEMPLOYED) {
            return false; // Already unlocked
        }

        TownJob existingJob = townJobs.get(jobType);
        if (existingJob != null && existingJob.isUnlocked()) {
            return false; // Already unlocked
        }

        TownJob job = new TownJob(jobType);
        job.requestUnlock(requestedBy);
        townJobs.put(jobType, job);
        markChanged(ASPECT_JOBS);
        return true;
    }

    /**
     * Gets all unlocked jobs for this town.
     *
     * @return List of unlocked jobs
     */
    public List<TownJob> getUnlockedJobs() {
        return townJobs.values().stream()
                .filter(TownJob::isUnlocked)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Calculates the total daily income from all unlocked jobs.
     *
     * @return Total daily income
     */
    public long getTotalDailyIncome() {
        return townJobs.values().stream()
                .filter(TownJob::isUnlocked)
                .mapToLong(job -> job.getJobType().getDailyPay())
                .sum();
    }

    /**
     * Checks if a specific job is unlocked.
     *
     * @param jobType The job type to check
     * @return true if the job is unlocked, false otherwise
     */
    public boolean isJobUnlocked(TownJob.JobType jobType) {
        TownJob job = townJobs.get(jobType);
        return job != null && job.isUnlocked();
    }

    /**
     * Gets the set of approved buildings for this town.
     *
     * @return Set of approved building types
     */
    public Set<TownRequest.RequestType> getApprovedBuildings() {
        return new HashSet<>(approvedBuildings);
    }

    /**
     * Checks if a specific building has been approved for this town.
     *
     * @param buildingType The building type to check
     * @return true if the building is approved, false otherwise
     */
    public boolean isBuildingApproved(TownRequest.RequestType buildingType) {
        return approvedBuildings.contains(buildingType);
    }

    /**
     * Approves a building for this town.
     *
     * @param buildingType The building type to approve
     */
    public void approveBuilding(TownRequest.RequestType buildingType) {
        if (approvedBuildings.add(buildingType)) {
            markChanged(ASPECT_BUILDINGS);
        }
    }

    /**
     * Removes an approved building from this town (for admin purposes).
     *
     * @param buildingType The building type to remove
     */
    public void removeApprovedBuilding(TownRequest.RequestType buildingType) {
        if (approvedBuildings.remove(buildingType)) {
            markChanged(ASPECT_BUILDINGS);
        }
    }

    /**
     * Gets the next building that can be requested based on the building order.
     *
     * @return The next building type that can be requested, or null if all are approved
     */
    public TownRequest.RequestType getNextAvailableBuilding() {
        TownRequest.RequestType[] buildingOrder = TownRequest.RequestType.getBuildingOrder();

        for (TownRequest.RequestType buildingType : buildingOrder) {
            if (!isBuildingApproved(buildingType)) {
                return buildingType;
            }
        }

        return null; // All buildings are approved
    }

    /**
     * Checks if a building can be requested based on prerequisites.
     *
     * @param buildingType The building type to check
     * @return true if the building can be requested, false otherwise
     */
    public boolean canRequestBuilding(TownRequest.RequestType buildingType) {
        TownRequest.RequestType[] buildingOrder = TownRequest.RequestType.getBuildingOrder();

        // Find the position of the requested building in the order
        int requestedIndex = -1;
        for (int i = 0; i < buildingOrder.length; i++) {
            if (buildingOrder[i] == buildingType) {
                requestedIndex = i;
                break;
            }
        }

        if (requestedIndex == -1) {
            return false; // Building not found in order
        }

        // Check if all previous buildings are approved
        for (int i = 0; i < requestedIndex; i++) {
            if (!isBuildingApproved(buildingOrder[i])) {
                return false; // Previous building not approved
            }
        }

        // Check if this building is already approved
        return !isBuildingApproved(buildingType);
    }
}
