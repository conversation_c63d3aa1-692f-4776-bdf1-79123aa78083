package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownJob;
import com.pokecobble.town.client.ClientTownJobsManager;
import com.pokecobble.town.sound.SoundUtil;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced Jobs Screen with multiple categories, progress tracking, and interactive elements.
 * Replaces the expandable jobs feature in MyTownScreen with a dedicated interface.
 */
@Environment(EnvType.CLIENT)
public class JobsScreen extends Screen {
    private final Screen parent;
    private final Town town;

    // Glass Effect Color Palette - matching MyTownScreen exactly
    private static final int GLASS_PANEL_BG = 0xD0101010;      // Main panel background
    private static final int GLASS_HEADER_BG = 0x60404040;     // Header glass background
    private static final int GLASS_SIDEBAR_BG = 0x60303030;    // Sidebar glass background
    private static final int GLASS_CONTENT_BG = 0x30000000;    // Content area background
    private static final int GLASS_CARD_BG = 0x40303030;       // Card backgrounds
    private static final int GLASS_CARD_HOVER = 0x60404040;    // Card hover state

    // Glass effect highlights and shadows - matching MyTownScreen
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;    // Top glass highlight
    private static final int GLASS_LEFT_HIGHLIGHT = 0x20FFFFFF;   // Left glass highlight
    private static final int GLASS_BRIGHT_HIGHLIGHT = 0x40FFFFFF; // Brighter highlights
    private static final int GLASS_INNER_HIGHLIGHT = 0x30FFFFFF;  // Inner glass highlights
    private static final int GLASS_SHADOW = 0x40000000;          // Glass shadows
    private static final int GLASS_BOTTOM_SHADOW = 0x20000000;   // Bottom shadows

    // Text colors - matching MyTownScreen
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text
    private static final int TEXT_SUCCESS = 0xFF4CAF50;
    private static final int TEXT_WARNING = 0xFFFF9800;
    private static final int TEXT_ERROR = 0xFFE53935;

    // Job category colors
    private static final int COLOR_AVAILABLE = 0xFF4CAF50;    // Green
    private static final int COLOR_UNLOCKED = 0xFF2196F3;     // Blue
    private static final int COLOR_LOCKED = 0xFF9E9E9E;       // Gray
    private static final int COLOR_APPLIED = 0xFFFF9800;      // Orange
    private static final int COLOR_STATISTICS = 0xFF9C27B0;   // Purple

    // Panel dimensions - exactly matching MyTownScreen
    private int panelWidth = 600;
    private int panelHeight = 400;
    private int leftX;
    private int topY;

    // Layout constants - matching MyTownScreen exactly
    private static final int HEADER_HEIGHT = 24;        // Matching MyTownScreen header (reduced from 32 to 24)
    private static final int SIDEBAR_WIDTH = 90;        // Matching MyTownScreen sidebar (reduced from 130 to 90)
    private static final int TAB_HEIGHT = 26;           // Matching MyTownScreen subcategory height
    private static final int TAB_SPACING = 3;           // Matching MyTownScreen subcategory spacing
    private static final int CARD_HEIGHT = 80;          // Card height for job items
    private static final int CARD_SPACING = 4;          // Spacing between cards

    // Spacing system for layout consistency
    private static final int SPACING_XS = 4;
    private static final int SPACING_SM = 8;
    private static final int SPACING_MD = 12;
    private static final int SPACING_LG = 16;

    // Job categories
    public enum JobCategory {
        AVAILABLE("Available Jobs", "Jobs you can apply for", COLOR_AVAILABLE, "💼"),
        UNLOCKED("Unlocked Jobs", "Jobs available to your town", COLOR_UNLOCKED, "🔓"),
        LOCKED("Locked Jobs", "Jobs requiring unlock", COLOR_LOCKED, "🔒"),
        BUILDING("Building", "Request infrastructure for jobs", COLOR_STATISTICS, "🏗"),
        MY_APPLICATIONS("My Applications", "Jobs you've applied for", COLOR_APPLIED, "📋"),
        STATISTICS("Job Statistics", "Performance and analytics", COLOR_STATISTICS, "📊");

        private final String displayName;
        private final String description;
        private final int color;
        private final String icon;

        JobCategory(String displayName, String description, int color, String icon) {
            this.displayName = displayName;
            this.description = description;
            this.color = color;
            this.icon = icon;
        }

        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
        public int getColor() { return color; }
        public String getIcon() { return icon; }
    }

    // Current state
    private JobCategory selectedCategory = JobCategory.AVAILABLE;
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 20;

    // Animation states
    private final float[] tabAnimations = new float[JobCategory.values().length];
    private final boolean[] tabHovered = new boolean[JobCategory.values().length];

    // Status message
    private Text statusText = Text.empty();
    private int statusColor = TEXT_PRIMARY;
    private int statusTimer = 0;

    public JobsScreen(Screen parent, Town town) {
        super(Text.literal("Town Jobs"));
        this.parent = parent;
        this.town = town;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions matching MyTownScreen approach
        panelWidth = Math.min(width - 20, 800);  // Responsive width like MyTownScreen
        panelHeight = height - 20;               // Full height like MyTownScreen
        leftX = (width - panelWidth) / 2;
        topY = 10;                               // Top positioning like MyTownScreen
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render dark background
        this.renderBackground(context);

        // Update animations
        updateAnimations();

        // Calculate positions - matching MyTownScreen
        leftX = (width - panelWidth) / 2;
        topY = 10; // Match MyTownScreen top position

        // Draw glass effect panel matching MyTownScreen style
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw glass effect header - matching MyTownScreen
        drawGlassHeader(context, leftX, topY, panelWidth, HEADER_HEIGHT, mouseX, mouseY);

        // Draw sidebar with glass effect
        drawGlassSidebar(context, mouseX, mouseY);

        // Draw main content area with glass effect
        drawGlassContent(context, mouseX, mouseY);

        // Draw status message if present
        if (statusTimer > 0) {
            drawStatusMessage(context);
            statusTimer--;
        }

        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Updates all animations.
     */
    private void updateAnimations() {
        float animationSpeed = 0.15f;

        // Update tab animations
        for (int i = 0; i < tabAnimations.length; i++) {
            if (tabHovered[i]) {
                tabAnimations[i] = Math.min(1.0f, tabAnimations[i] + animationSpeed);
            } else {
                tabAnimations[i] = Math.max(0.0f, tabAnimations[i] - animationSpeed);
            }
        }
    }

    /**
     * Draws a glass effect panel matching MyTownScreen styling
     */
    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Main panel background - darker glass effect
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);

        // Glass effect borders - matching MyTownScreen style
        context.fill(x, y, x + width, y + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW); // Bottom shadow

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);
    }

    /**
     * Draws the glass effect header section matching MyTownScreen styling
     */
    private void drawGlassHeader(DrawContext context, int x, int y, int width, int height, int mouseX, int mouseY) {
        // Header background with glass effect - matching MyTownScreen headers
        context.fill(x, y, x + width, y + height, GLASS_HEADER_BG);

        // Glass effect borders - exactly like MyTownScreen headers
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);

        // Header title
        String title = "🏢 " + town.getName() + " Jobs";
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = x + (width - titleWidth) / 2;
        int titleY = y + (height - 8) / 2;

        // Subtle title glow effect
        context.fillGradient(
            titleX - 4, titleY - 1,
            titleX + titleWidth + 4, titleY + 9,
            0x20FFFFFF, 0x00FFFFFF
        );

        context.drawTextWithShadow(this.textRenderer, Text.literal(title).formatted(Formatting.BOLD),
                titleX, titleY, TEXT_PRIMARY);

        // Back button with glass effect
        int backButtonX = x + 8;
        int backButtonY = y + (height - 16) / 2;
        boolean backHovered = mouseX >= backButtonX && mouseX <= backButtonX + 50 &&
                             mouseY >= backButtonY && mouseY <= backButtonY + 16;

        drawGlassButton(context, backButtonX, backButtonY, 50, 16, TEXT_ERROR, backHovered, "← Back");
    }

    /**
     * Draws the sidebar with glass effect matching MyTownScreen styling
     */
    private void drawGlassSidebar(DrawContext context, int mouseX, int mouseY) {
        int sidebarX = leftX + 5; // Moved more to the left like MyTownScreen
        int sidebarY = topY + HEADER_HEIGHT + 5; // Start after header with small gap
        int sidebarHeight = panelHeight - (HEADER_HEIGHT + 15); // Account for header and bottom margin

        // Draw glass effect sidebar background matching MyTownScreen style
        context.fill(sidebarX, sidebarY, sidebarX + SIDEBAR_WIDTH, sidebarY + sidebarHeight, GLASS_SIDEBAR_BG);

        // Glass effect borders
        context.fill(sidebarX, sidebarY, sidebarX + SIDEBAR_WIDTH, sidebarY + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
        context.fill(sidebarX, sidebarY, sidebarX + 1, sidebarY + sidebarHeight, GLASS_INNER_HIGHLIGHT); // Left highlight
        context.fill(sidebarX + SIDEBAR_WIDTH - 1, sidebarY, sidebarX + SIDEBAR_WIDTH, sidebarY + sidebarHeight, GLASS_TOP_HIGHLIGHT); // Right highlight
        context.fill(sidebarX, sidebarY + sidebarHeight - 1, sidebarX + SIDEBAR_WIDTH, sidebarY + sidebarHeight, GLASS_SHADOW); // Bottom shadow

        // Inner glass effect for depth
        context.fill(sidebarX + 1, sidebarY + 1, sidebarX + SIDEBAR_WIDTH - 1, sidebarY + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(sidebarX + 1, sidebarY + 1, sidebarX + 2, sidebarY + sidebarHeight - 1, 0x15FFFFFF);

        // Draw category tabs with glass effect
        int tabY = sidebarY + 5;
        JobCategory[] categories = JobCategory.values();

        for (int i = 0; i < categories.length; i++) {
            JobCategory category = categories[i];

            // Check if tab is hovered
            tabHovered[i] = mouseX >= sidebarX + 5 && mouseX <= sidebarX + SIDEBAR_WIDTH - 5 &&
                           mouseY >= tabY && mouseY <= tabY + TAB_HEIGHT;

            // Draw tab with glass effect
            drawGlassCategoryTab(context, category, sidebarX + 5, tabY, SIDEBAR_WIDTH - 10,
                          TAB_HEIGHT, category == selectedCategory, tabAnimations[i]);

            tabY += TAB_HEIGHT + TAB_SPACING;
        }
    }

    /**
     * Draws the main content area with glass effect matching MyTownScreen styling
     */
    private void drawGlassContent(DrawContext context, int mouseX, int mouseY) {
        int contentX = leftX + 5 + SIDEBAR_WIDTH + 5; // Position after sidebar
        int contentY = topY + HEADER_HEIGHT + 5; // Start after header with small gap
        int contentWidth = panelWidth - (5 + SIDEBAR_WIDTH + 5) - 10; // Fill remaining width
        int contentHeight = panelHeight - (HEADER_HEIGHT + 15); // Account for header and bottom margin

        // Draw glass effect content area background matching MyTownScreen style
        context.fill(contentX, contentY, contentX + contentWidth, contentY + contentHeight, GLASS_CONTENT_BG);

        // Glass effect borders
        context.fill(contentX, contentY, contentX + contentWidth, contentY + 1, GLASS_BRIGHT_HIGHLIGHT); // Top border
        context.fill(contentX, contentY, contentX + 1, contentY + contentHeight, GLASS_BRIGHT_HIGHLIGHT); // Left border

        // Draw content based on selected category
        switch (selectedCategory) {
            case AVAILABLE:
                drawAvailableJobs(context, contentX, contentY, contentWidth, contentHeight, mouseX, mouseY);
                break;
            case UNLOCKED:
                drawUnlockedJobs(context, contentX, contentY, contentWidth, contentHeight, mouseX, mouseY);
                break;
            case LOCKED:
                drawLockedJobs(context, contentX, contentY, contentWidth, contentHeight, mouseX, mouseY);
                break;
            case BUILDING:
                drawBuilding(context, contentX, contentY, contentWidth, contentHeight, mouseX, mouseY);
                break;
            case MY_APPLICATIONS:
                drawMyApplications(context, contentX, contentY, contentWidth, contentHeight, mouseX, mouseY);
                break;
            case STATISTICS:
                drawStatistics(context, contentX, contentY, contentWidth, contentHeight, mouseX, mouseY);
                break;
        }
    }

    /**
     * Draws a category tab with glass effect matching MyTownScreen subcategory styling
     */
    private void drawGlassCategoryTab(DrawContext context, JobCategory category, int x, int y, int width, int height,
                                     boolean isSelected, float hoverAnimation) {
        // Glass effect tab background
        int bgColor;
        if (isSelected) {
            bgColor = category.getColor();
        } else if (hoverAnimation > 0) {
            bgColor = GLASS_CARD_HOVER;
        } else {
            bgColor = GLASS_CARD_BG;
        }

        context.fill(x, y, x + width, y + height, bgColor);

        // Glass effect highlights - matching MyTownScreen subcategory style
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW);
        context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW);

        // Icon and text - compact for narrower sidebar
        String icon = category.getIcon();
        String name = getShortDisplayName(category);

        int iconX = x + 4;
        int textX = x + 16;
        int textY = y + (height - 8) / 2;

        context.drawTextWithShadow(this.textRenderer, icon, iconX, textY, TEXT_PRIMARY);
        context.drawTextWithShadow(this.textRenderer, Text.literal(name).formatted(
                isSelected ? Formatting.BOLD : Formatting.RESET), textX, textY,
                isSelected ? TEXT_PRIMARY : TEXT_SECONDARY);
    }

    /**
     * Gets shorter display names for compact tabs.
     */
    private String getShortDisplayName(JobCategory category) {
        switch (category) {
            case AVAILABLE: return "Available";
            case UNLOCKED: return "Unlocked";
            case LOCKED: return "Locked";
            case MY_APPLICATIONS: return "Applications";
            case STATISTICS: return "Stats";
            default: return category.getDisplayName();
        }
    }

    /**
     * Wraps text to fit within the specified width, breaking at word boundaries.
     */
    private List<String> wrapText(String text, int maxWidth) {
        List<String> lines = new ArrayList<>();
        if (text == null || text.isEmpty()) {
            return lines;
        }

        String[] words = text.split(" ");
        StringBuilder currentLine = new StringBuilder();

        for (String word : words) {
            String testLine = currentLine.length() == 0 ? word : currentLine + " " + word;
            int testWidth = this.textRenderer.getWidth(testLine);

            if (testWidth <= maxWidth) {
                currentLine = new StringBuilder(testLine);
            } else {
                // Current line is full, start a new line
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder(word);
                } else {
                    // Single word is too long, truncate it
                    String truncated = word;
                    while (this.textRenderer.getWidth(truncated + "...") > maxWidth && truncated.length() > 1) {
                        truncated = truncated.substring(0, truncated.length() - 1);
                    }
                    lines.add(truncated + (truncated.length() < word.length() ? "..." : ""));
                }
            }
        }

        // Add the last line if it has content
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        return lines;
    }

    /**
     * Draws available jobs content.
     */
    private void drawAvailableJobs(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight,
                                  int mouseX, int mouseY) {
        // Header
        drawContentHeader(context, contentX, contentY, contentWidth, "Available Jobs",
                         "Jobs you can apply for right now");

        // Get available jobs
        ClientTownJobsManager jobsManager = ClientTownJobsManager.getInstance();
        if (!jobsManager.hasJobsData()) {
            drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                          "Loading job data...", "Please wait while we fetch the latest job information.");
            return;
        }

        List<TownJob.JobType> availableJobs = getAvailableJobs(jobsManager);
        if (availableJobs.isEmpty()) {
            drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                          "No jobs available", "All jobs are either locked or you've already applied.");
            return;
        }

        // Draw job cards with smaller header offset
        drawJobCards(context, contentX, contentY + 40, contentWidth, contentHeight - 40,
                    availableJobs, jobsManager, mouseX, mouseY, true);
    }

    /**
     * Draws unlocked jobs content.
     */
    private void drawUnlockedJobs(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight,
                                 int mouseX, int mouseY) {
        drawContentHeader(context, contentX, contentY, contentWidth, "Unlocked Jobs",
                         "Jobs available to your town");

        ClientTownJobsManager jobsManager = ClientTownJobsManager.getInstance();
        if (!jobsManager.hasJobsData()) {
            drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                          "Loading job data...", "Please wait while we fetch the latest job information.");
            return;
        }

        List<TownJob.JobType> unlockedJobs = getUnlockedJobs(jobsManager);
        if (unlockedJobs.isEmpty()) {
            drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                          "No unlocked jobs", "Your town hasn't unlocked any jobs yet.");
            return;
        }

        drawJobCards(context, contentX, contentY + 40, contentWidth, contentHeight - 40,
                    unlockedJobs, jobsManager, mouseX, mouseY, false);
    }

    /**
     * Draws locked jobs content.
     */
    private void drawLockedJobs(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight,
                               int mouseX, int mouseY) {
        drawContentHeader(context, contentX, contentY, contentWidth, "Locked Jobs",
                         "Jobs requiring unlock fees");

        ClientTownJobsManager jobsManager = ClientTownJobsManager.getInstance();
        if (!jobsManager.hasJobsData()) {
            drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                          "Loading job data...", "Please wait while we fetch the latest job information.");
            return;
        }

        List<TownJob.JobType> lockedJobs = getLockedJobs(jobsManager);
        if (lockedJobs.isEmpty()) {
            drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                          "All jobs unlocked!", "Your town has unlocked all available jobs.");
            return;
        }

        drawJobCards(context, contentX, contentY + 40, contentWidth, contentHeight - 40,
                    lockedJobs, jobsManager, mouseX, mouseY, false);
    }

    /**
     * Draws my applications content.
     */
    private void drawMyApplications(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight,
                                   int mouseX, int mouseY) {
        drawContentHeader(context, contentX, contentY, contentWidth, "My Applications",
                         "Jobs you've applied for");

        // TODO: Implement application tracking
        drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                      "No applications yet", "Apply for jobs to see your applications here.");
    }

    /**
     * Draws the building category with compact design matching MyTownScreen style and scrolling support.
     */
    private void drawBuilding(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight,
                             int mouseX, int mouseY) {
        // Modern layout with integrated header (matching MyTownScreen subcategories)
        int leftMargin = contentX + 5; // Reduced margin for more space
        int headerHeight = 32; // Taller header for better integration
        int currentY = contentY + 5; // Start closer to top

        // Draw modern header background with glass effect
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + headerHeight, GLASS_CARD_BG);
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 10, currentY + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(leftMargin, currentY, leftMargin + 1, currentY + headerHeight, GLASS_TOP_HIGHLIGHT); // Left highlight

        // Header layout - distribute controls across the header
        int headerPadding = 8;
        int controlY = currentY + (headerHeight - 16) / 2; // Center controls vertically in header
        int currentX = leftMargin + headerPadding;

        // Header title
        context.drawTextWithShadow(this.textRenderer, Text.literal("🏗 Building").formatted(Formatting.BOLD),
                currentX, currentY + 8, TEXT_PRIMARY);

        // Header subtitle
        context.drawTextWithShadow(this.textRenderer, "Request infrastructure for jobs",
                currentX, currentY + 20, TEXT_SECONDARY);

        // Request Building button in header (right side)
        int buttonWidth = 120;
        int buttonHeight = 18;
        int buttonX = leftMargin + contentWidth - 15 - buttonWidth - headerPadding;
        int buttonY = controlY;

        boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        drawGlassButton(context, buttonX, buttonY, buttonWidth, buttonHeight,
                       COLOR_STATISTICS, buttonHovered, "Request Building");

        currentY += headerHeight + 5; // Move past header with small gap

        // Calculate scrollable area dimensions
        int listAreaY = currentY;
        int listAreaHeight = contentHeight - (currentY - contentY) - 10; // Use remaining space with small bottom margin

        // Enable scissor for scrolling content
        context.enableScissor(leftMargin, listAreaY, leftMargin + contentWidth - 10, listAreaY + listAreaHeight);

        // Apply scroll offset
        int scrolledY = listAreaY - scrollOffset;

        // Compact explanation section
        drawCompactExplanation(context, leftMargin, scrolledY, contentWidth - 15, mouseX, mouseY);
        scrolledY += 65; // Fixed height for explanation + spacing

        // Compact job requirements list
        drawCompactJobRequirements(context, leftMargin, scrolledY, contentWidth - 15, listAreaHeight - 65);

        context.disableScissor();

        // Draw scrollbar if needed
        int totalContentHeight = 65 + calculateJobRequirementsHeight(); // Explanation + actual job requirements height
        int maxScroll = Math.max(0, totalContentHeight - listAreaHeight);
        if (maxScroll > 0) {
            int scrollbarX = leftMargin + contentWidth - 15; // Position scrollbar on right edge
            drawScrollbar(context, scrollbarX, listAreaY, 4, listAreaHeight, scrollOffset, maxScroll);
        }
    }

    /**
     * Draws a compact explanation section with the updated process description.
     */
    private void drawCompactExplanation(DrawContext context, int x, int y, int width, int mouseX, int mouseY) {
        // Glass effect background for explanation
        context.fill(x, y, x + width, y + 55, GLASS_CARD_BG);
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y, x + 1, y + 55, GLASS_TOP_HIGHLIGHT);

        // Title
        context.drawTextWithShadow(this.textRenderer, Text.literal("How it works:").formatted(Formatting.BOLD),
                x + 8, y + 6, TEXT_PRIMARY);

        // Updated explanation text as requested
        String[] explanationLines = {
            "1. Town builds the required infrastructure first",
            "2. Admin reviews and verifies the construction",
            "3. Admin judges if the building meets requirements"
        };

        int lineY = y + 18;
        for (String line : explanationLines) {
            context.drawTextWithShadow(this.textRenderer, line, x + 8, lineY, TEXT_SECONDARY);
            lineY += 12;
        }
    }

    /**
     * Draws a compact job requirements list matching MyTownScreen style with proper content sizing.
     */
    private void drawCompactJobRequirements(DrawContext context, int x, int y, int width, int availableHeight) {
        // Calculate actual content height needed
        int contentHeight = calculateJobRequirementsHeight();
        int actualHeight = Math.min(contentHeight, availableHeight);

        // Glass effect background
        context.fill(x, y, x + width, y + actualHeight, GLASS_CARD_BG);
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y, x + 1, y + actualHeight, GLASS_TOP_HIGHLIGHT);

        // Title
        context.drawTextWithShadow(this.textRenderer, Text.literal("Required Buildings:").formatted(Formatting.BOLD),
                x + 8, y + 6, TEXT_PRIMARY);

        // Compact list of job requirements - draw all items without height restriction
        int currentY = y + 20;
        int lineHeight = 14;

        for (TownJob.JobType jobType : TownJob.JobType.values()) {
            if (jobType == TownJob.JobType.UNEMPLOYED) continue;

            String requiredBuilding = jobType.getRequiredBuilding();
            if (requiredBuilding != null) {
                // Job icon and name
                String jobInfo = jobType.getIcon() + " " + jobType.getDisplayName() + " → " + requiredBuilding;
                context.drawTextWithShadow(this.textRenderer, jobInfo, x + 8, currentY, TEXT_SECONDARY);
                currentY += lineHeight;
            }
        }
    }

    /**
     * Calculates the actual height needed for the job requirements section.
     */
    private int calculateJobRequirementsHeight() {
        int height = 20; // Title space
        int lineHeight = 14;

        // Count jobs that need buildings
        for (TownJob.JobType jobType : TownJob.JobType.values()) {
            if (jobType == TownJob.JobType.UNEMPLOYED) continue;

            String requiredBuilding = jobType.getRequiredBuilding();
            if (requiredBuilding != null) {
                height += lineHeight;
            }
        }

        height += 10; // Bottom padding
        return height;
    }







    /**
     * Draws statistics content.
     */
    private void drawStatistics(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight,
                               int mouseX, int mouseY) {
        drawContentHeader(context, contentX, contentY, contentWidth, "Job Statistics",
                         "Performance and analytics");

        // TODO: Implement job statistics
        drawEmptyState(context, contentX, contentY, contentWidth, contentHeight,
                      "Statistics coming soon", "Job performance tracking will be available in a future update.");
    }

    /**
     * Draws a content header with glass effect styling.
     */
    private void drawContentHeader(DrawContext context, int x, int y, int width, String title, String subtitle) {
        // Glass effect header background
        context.fill(x + SPACING_SM, y + SPACING_SM, x + width - SPACING_SM, y + 35, GLASS_CARD_BG);
        context.fill(x + SPACING_SM, y + SPACING_SM, x + width - SPACING_SM, y + SPACING_SM + 1, GLASS_TOP_HIGHLIGHT);

        // Title with smaller spacing
        context.drawTextWithShadow(this.textRenderer, Text.literal(title).formatted(Formatting.BOLD),
                x + SPACING_MD, y + SPACING_SM + 4, TEXT_PRIMARY);

        // Subtitle with tighter spacing
        context.drawTextWithShadow(this.textRenderer, subtitle,
                x + SPACING_MD, y + SPACING_SM + 16, TEXT_SECONDARY);
    }

    /**
     * Draws an empty state message.
     */
    private void drawEmptyState(DrawContext context, int x, int y, int width, int height, String title, String message) {
        int centerX = x + width / 2;
        int centerY = y + height / 2;

        // Title
        int titleWidth = this.textRenderer.getWidth(title);
        context.drawTextWithShadow(this.textRenderer, Text.literal(title).formatted(Formatting.BOLD),
                centerX - titleWidth / 2, centerY - 20, TEXT_MUTED);

        // Message
        int messageWidth = this.textRenderer.getWidth(message);
        context.drawTextWithShadow(this.textRenderer, message,
                centerX - messageWidth / 2, centerY, TEXT_MUTED);
    }

    /**
     * Draws job cards in a grid layout with MyTownScreen proportions.
     */
    private void drawJobCards(DrawContext context, int x, int y, int width, int height,
                             List<TownJob.JobType> jobs, ClientTownJobsManager jobsManager,
                             int mouseX, int mouseY, boolean showApplyButton) {
        // Enable scissor for scrolling
        context.enableScissor(x, y, x + width, y + height);

        // Smaller cards to fit MyTownScreen proportions - 2 cards per row max
        int cardsPerRow = Math.max(1, Math.min(2, (width - SPACING_SM * 2) / (200 + CARD_SPACING)));
        int cardWidth = (width - SPACING_SM * 2 - (cardsPerRow - 1) * CARD_SPACING) / cardsPerRow;

        int currentX = x + SPACING_SM;
        int currentY = y + SPACING_SM - scrollOffset;
        int cardsInCurrentRow = 0;

        for (TownJob.JobType jobType : jobs) {
            TownJob job = jobsManager.getTownJob(jobType);

            // Check if card is visible
            if (currentY + CARD_HEIGHT >= y && currentY <= y + height) {
                boolean isHovered = mouseX >= currentX && mouseX <= currentX + cardWidth &&
                                   mouseY >= currentY && mouseY <= currentY + CARD_HEIGHT &&
                                   mouseY >= y && mouseY <= y + height;

                drawJobCard(context, jobType, job, currentX, currentY, cardWidth, CARD_HEIGHT,
                           isHovered, showApplyButton);
            }

            cardsInCurrentRow++;
            if (cardsInCurrentRow >= cardsPerRow) {
                // Move to next row
                currentX = x + SPACING_SM;
                currentY += CARD_HEIGHT + CARD_SPACING;
                cardsInCurrentRow = 0;
            } else {
                // Move to next column
                currentX += cardWidth + CARD_SPACING;
            }
        }

        context.disableScissor();

        // Draw scrollbar if needed with proper calculations
        int totalRows = (jobs.size() + cardsPerRow - 1) / cardsPerRow;
        int totalHeight = totalRows * (CARD_HEIGHT + CARD_SPACING) + SPACING_SM;
        int maxScroll = Math.max(0, totalHeight - height);

        if (maxScroll > 0) {
            drawScrollbar(context, x + width - 10, y, 6, height, scrollOffset, maxScroll);
        }
    }

    /**
     * Draws an individual job card with glass effect styling.
     */
    private void drawJobCard(DrawContext context, TownJob.JobType jobType, TownJob job,
                            int x, int y, int width, int height, boolean isHovered, boolean showApplyButton) {
        // Glass effect card background
        int bgColor = isHovered ? GLASS_CARD_HOVER : GLASS_CARD_BG;
        context.fill(x, y, x + width, y + height, bgColor);

        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);           // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT);          // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW); // Right shadow
        context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW); // Bottom shadow

        // Job type accent border on left with glass effect
        context.fill(x, y, x + 2, y + height, jobType.getColor());

        // Job icon and name with smaller spacing
        int iconX = x + SPACING_SM;
        int iconY = y + SPACING_SM;
        context.drawTextWithShadow(this.textRenderer, jobType.getIcon(), iconX, iconY, jobType.getColor());

        int nameX = iconX + 16;  // Smaller icon spacing
        context.drawTextWithShadow(this.textRenderer, Text.literal(jobType.getDisplayName()).formatted(Formatting.BOLD),
                nameX, iconY, TEXT_PRIMARY);

        // Job description with automatic text wrapping
        String description = jobType.getDescription();
        int descriptionWidth = width - (SPACING_SM * 2);  // Available width for text
        List<String> wrappedLines = wrapText(description, descriptionWidth);

        int descY = y + SPACING_SM + 12;
        for (int i = 0; i < Math.min(wrappedLines.size(), 2); i++) {  // Max 2 lines for compact cards
            context.drawTextWithShadow(this.textRenderer, wrappedLines.get(i),
                    x + SPACING_SM, descY + (i * 10), TEXT_SECONDARY);
        }

        // Job details with spacing adjusted for multi-line descriptions
        int detailsY = y + SPACING_SM + 32;  // More space for 2-line descriptions

        // Daily pay with smaller text
        context.drawTextWithShadow(this.textRenderer, "$" + jobType.getDailyPay() + "/day",
                x + SPACING_SM, detailsY, TEXT_SUCCESS);

        // Max slots - shorter text
        String slotsText = jobType.getMaxSlots() == -1 ? "∞ slots" :
                          jobType.getMaxSlots() + " slots";
        context.drawTextWithShadow(this.textRenderer, slotsText,
                x + SPACING_SM, detailsY + 10, TEXT_MUTED);

        // Status and buttons with smaller dimensions
        int buttonY = y + height - 22;  // Smaller button area
        if (job != null && job.isUnlocked()) {
            if (showApplyButton) {
                // Apply button - smaller
                boolean buttonHovered = isHovered;
                drawGlassButton(context, x + width - 60, buttonY, 55, 16, COLOR_AVAILABLE, buttonHovered, "Apply");
            } else {
                // Status indicator - smaller
                context.drawTextWithShadow(this.textRenderer, "✓ Unlocked",
                        x + width - 55, buttonY + 4, TEXT_SUCCESS);
            }
        } else {
            // Unlock fee - shorter text, positioned properly
            context.drawTextWithShadow(this.textRenderer, "$" + jobType.getUnlockFee() + " unlock",
                    x + SPACING_SM, detailsY + 20, TEXT_WARNING);

            if (!showApplyButton) {
                // Unlock button - smaller
                boolean buttonHovered = isHovered;
                drawGlassButton(context, x + width - 60, buttonY, 55, 16, COLOR_LOCKED, buttonHovered, "Unlock");
            }
        }
    }

    /**
     * Gets available jobs (unlocked and not applied to).
     */
    private List<TownJob.JobType> getAvailableJobs(ClientTownJobsManager jobsManager) {
        List<TownJob.JobType> available = new ArrayList<>();
        for (TownJob.JobType jobType : TownJob.JobType.values()) {
            if (jobType == TownJob.JobType.UNEMPLOYED) continue;

            TownJob job = jobsManager.getTownJob(jobType);
            if (job != null && job.isUnlocked()) {
                // TODO: Check if player has already applied
                available.add(jobType);
            }
        }
        return available;
    }

    /**
     * Gets unlocked jobs.
     */
    private List<TownJob.JobType> getUnlockedJobs(ClientTownJobsManager jobsManager) {
        List<TownJob.JobType> unlocked = new ArrayList<>();
        for (TownJob.JobType jobType : TownJob.JobType.values()) {
            if (jobType == TownJob.JobType.UNEMPLOYED) continue;

            TownJob job = jobsManager.getTownJob(jobType);
            if (job != null && job.isUnlocked()) {
                unlocked.add(jobType);
            }
        }
        return unlocked;
    }

    /**
     * Gets locked jobs.
     */
    private List<TownJob.JobType> getLockedJobs(ClientTownJobsManager jobsManager) {
        List<TownJob.JobType> locked = new ArrayList<>();
        for (TownJob.JobType jobType : TownJob.JobType.values()) {
            if (jobType == TownJob.JobType.UNEMPLOYED) continue;

            TownJob job = jobsManager.getTownJob(jobType);
            if (job == null || !job.isUnlocked()) {
                locked.add(jobType);
            }
        }
        return locked;
    }

    /**
     * Draws a glass button with glass effect matching MyTownScreen styling.
     */
    private void drawGlassButton(DrawContext context, int x, int y, int width, int height,
                                int color, boolean isHovered, String text) {
        // Glass effect button background
        int bgColor = isHovered ? GLASS_CARD_HOVER : GLASS_CARD_BG;
        context.fill(x, y, x + width, y + height, bgColor);

        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight

        if (isHovered) {
            // Enhanced glass effect for hovered buttons
            context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW); // Right shadow
            context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW); // Bottom shadow
        }

        // Subtle color accent overlay
        int accentAlpha = isHovered ? 0x30 : 0x20;
        int accentOverlay = (accentAlpha << 24) | (color & 0x00FFFFFF);
        context.fill(x + 1, y + 1, x + width - 1, y + height - 1, accentOverlay);

        // Draw text
        int textColor = isHovered ? TEXT_PRIMARY : TEXT_SECONDARY;
        context.drawCenteredTextWithShadow(this.textRenderer, text,
                x + width / 2, y + (height - 8) / 2, textColor);
    }

    /**
     * Draws a scrollbar with glass effect matching MyTownScreen styling.
     */
    private void drawScrollbar(DrawContext context, int x, int y, int width, int height,
                              int scrollOffset, int maxScroll) {
        // Glass effect track background
        context.fill(x, y, x + width, y + height, 0x20FFFFFF);

        // Glass effect handle
        int handleHeight = Math.max(20, height * height / (height + maxScroll));
        float scrollRatio = maxScroll > 0 ? (float)scrollOffset / maxScroll : 0;
        int handleY = y + (int)((height - handleHeight) * scrollRatio);

        // Ensure handle doesn't go out of bounds
        handleY = Math.max(y, Math.min(handleY, y + height - handleHeight));

        // Draw handle with glass effect
        context.fill(x, handleY, x + width, handleY + handleHeight, 0xC0FFFFFF);
    }

    /**
     * Draws status message with glass effect.
     */
    private void drawStatusMessage(DrawContext context) {
        int messageY = topY + panelHeight - 40;
        context.fill(leftX + SPACING_LG, messageY, leftX + panelWidth - SPACING_LG, messageY + 20, GLASS_CARD_BG);
        context.fill(leftX + SPACING_LG, messageY, leftX + panelWidth - SPACING_LG, messageY + 1, GLASS_TOP_HIGHLIGHT);

        context.drawCenteredTextWithShadow(this.textRenderer, statusText,
                leftX + panelWidth / 2, messageY + 6, statusColor);
    }

    /**
     * Sets a status message.
     */
    private void setStatus(String message, int color) {
        this.statusText = Text.literal(message);
        this.statusColor = color;
        this.statusTimer = 60; // 3 seconds
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check back button with smaller dimensions
            int backButtonX = leftX + SPACING_SM;
            int backButtonY = topY + (HEADER_HEIGHT - 18) / 2;
            if (mouseX >= backButtonX && mouseX <= backButtonX + 50 &&
                mouseY >= backButtonY && mouseY <= backButtonY + 18) {
                this.client.setScreen(parent);
                SoundUtil.playButtonClickSound();
                return true;
            }

            // Check category tabs with smaller dimensions
            int sidebarX = leftX;
            int sidebarY = topY + HEADER_HEIGHT;
            int tabY = sidebarY + SPACING_SM;
            JobCategory[] categories = JobCategory.values();

            for (int i = 0; i < categories.length; i++) {
                if (mouseX >= sidebarX + SPACING_XS && mouseX <= sidebarX + SIDEBAR_WIDTH - SPACING_XS &&
                    mouseY >= tabY && mouseY <= tabY + TAB_HEIGHT) {
                    selectedCategory = categories[i];
                    scrollOffset = 0; // Reset scroll when changing categories
                    SoundUtil.playButtonClickSound();
                    return true;
                }
                tabY += TAB_HEIGHT + SPACING_XS;
            }

            // Check for scrollbar clicks in content area
            int contentX = leftX + SIDEBAR_WIDTH;
            int contentY = topY + HEADER_HEIGHT;
            int contentWidth = panelWidth - SIDEBAR_WIDTH;
            int contentHeight = panelHeight - HEADER_HEIGHT;

            // Check if click is on scrollbar
            int scrollbarX = contentX + contentWidth - 10;
            if (mouseX >= scrollbarX && mouseX <= scrollbarX + 6 &&
                mouseY >= contentY && mouseY <= contentY + contentHeight) {

                int maxScroll = calculateMaxScroll(contentWidth, contentHeight - 40);
                if (maxScroll > 0) {
                    // Calculate new scroll position based on click position
                    float clickRatio = (float)(mouseY - contentY) / contentHeight;
                    scrollOffset = Math.round(clickRatio * maxScroll);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                    SoundUtil.playButtonClickSound();
                    return true;
                }
            }

            // Check building request button in header
            if (selectedCategory == JobCategory.BUILDING) {
                // Calculate button position matching the new header design
                int leftMargin = contentX + 5;
                int headerHeight = 32;
                int headerY = contentY + 5;
                int headerPadding = 8;
                int controlY = headerY + (headerHeight - 16) / 2; // Center controls vertically in header

                // Button positioning in header (right side)
                int buttonWidth = 120;
                int buttonHeight = 18;
                int buttonX = leftMargin + contentWidth - 15 - buttonWidth - headerPadding;
                int buttonY = controlY;

                // Check if click is within button bounds
                if (mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                    mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                    openBuildingRequestScreen();
                    SoundUtil.playButtonClickSound();
                    return true;
                }
            }

            // Handle job card clicks (apply/unlock buttons)
            handleJobCardClicks(mouseX, mouseY, contentX, contentY, contentWidth, contentHeight);
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Handles clicks on job cards, including unlock buttons that switch to building category.
     */
    private void handleJobCardClicks(double mouseX, double mouseY, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Only handle clicks for categories that show job cards
        if (selectedCategory != JobCategory.AVAILABLE && selectedCategory != JobCategory.UNLOCKED && selectedCategory != JobCategory.LOCKED) {
            return;
        }

        ClientTownJobsManager jobsManager = ClientTownJobsManager.getInstance();
        if (!jobsManager.hasJobsData()) {
            return;
        }

        // Get the appropriate job list based on current category
        List<TownJob.JobType> jobs;
        switch (selectedCategory) {
            case AVAILABLE:
                jobs = getAvailableJobs(jobsManager);
                break;
            case UNLOCKED:
                jobs = getUnlockedJobs(jobsManager);
                break;
            case LOCKED:
                jobs = getLockedJobs(jobsManager);
                break;
            default:
                return;
        }

        if (jobs.isEmpty()) {
            return;
        }

        // Calculate job card positions (matching drawJobCards logic)
        int x = contentX + SPACING_SM;
        int y = contentY + 40 + SPACING_SM - scrollOffset; // +40 for header
        int cardsPerRow = Math.max(1, Math.min(2, (contentWidth - SPACING_SM * 2) / (200 + CARD_SPACING)));
        int cardWidth = (contentWidth - SPACING_SM * 2 - (cardsPerRow - 1) * CARD_SPACING) / cardsPerRow;

        int currentX = x;
        int currentY = y;
        int cardsInCurrentRow = 0;

        for (TownJob.JobType jobType : jobs) {
            TownJob job = jobsManager.getTownJob(jobType);

            // Check if card is visible and mouse is within card bounds
            if (currentY + CARD_HEIGHT >= contentY && currentY <= contentY + contentHeight &&
                mouseX >= currentX && mouseX <= currentX + cardWidth &&
                mouseY >= currentY && mouseY <= currentY + CARD_HEIGHT &&
                mouseY >= contentY && mouseY <= contentY + contentHeight) {

                // Check if click is on unlock button (only for locked jobs)
                if (selectedCategory == JobCategory.LOCKED && (job == null || !job.isUnlocked())) {
                    int buttonX = currentX + cardWidth - 60;
                    int buttonY = currentY + CARD_HEIGHT - 22;
                    int buttonWidth = 55;
                    int buttonHeight = 16;

                    if (mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                        mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                        // Switch to building category when unlock button is clicked
                        selectedCategory = JobCategory.BUILDING;
                        scrollOffset = 0; // Reset scroll when changing categories
                        SoundUtil.playButtonClickSound();
                        return;
                    }
                }
                // TODO: Handle other button clicks (Apply buttons, etc.)
            }

            // Move to next card position (matching drawJobCards logic)
            cardsInCurrentRow++;
            if (cardsInCurrentRow >= cardsPerRow) {
                currentX = x;
                currentY += CARD_HEIGHT + CARD_SPACING;
                cardsInCurrentRow = 0;
            } else {
                currentX += cardWidth + CARD_SPACING;
            }
        }
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Check if mouse is in content area - using glass content layout
        int contentX = leftX + 5 + SIDEBAR_WIDTH + 5; // Position after sidebar
        int contentY = topY + HEADER_HEIGHT + 5; // Start after header with small gap
        int contentWidth = panelWidth - (5 + SIDEBAR_WIDTH + 5) - 10; // Fill remaining width
        int contentHeight = panelHeight - (HEADER_HEIGHT + 15); // Account for header and bottom margin

        if (mouseX >= contentX && mouseX <= contentX + contentWidth &&
            mouseY >= contentY && mouseY <= contentY + contentHeight) {

            // For Building category, check if mouse is in scrollable area
            if (selectedCategory == JobCategory.BUILDING) {
                int headerHeight = 32;
                int listAreaY = contentY + 5 + headerHeight + 5;
                int listAreaHeight = contentHeight - (5 + headerHeight + 5) - 10;

                // Check if mouse is in the scrollable list area
                if (mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {
                    int totalContentHeight = 65 + calculateJobRequirementsHeight(); // Explanation + actual job requirements height
                    int maxScroll = Math.max(0, totalContentHeight - listAreaHeight);

                    if (maxScroll > 0) {
                        scrollOffset -= (int)(amount * SCROLL_AMOUNT);
                        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                        return true;
                    }
                }
            } else {
                // Calculate max scroll based on actual content for other categories
                int maxScroll = calculateMaxScroll(contentWidth, contentHeight - 40); // Account for header

                if (maxScroll > 0) {
                    scrollOffset -= (int)(amount * SCROLL_AMOUNT);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                    return true;
                }
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Calculates the maximum scroll offset based on current content.
     */
    private int calculateMaxScroll(int contentWidth, int availableHeight) {
        // Get current job list based on selected category
        ClientTownJobsManager jobsManager = ClientTownJobsManager.getInstance();
        if (!jobsManager.hasJobsData()) {
            return 0;
        }

        List<TownJob.JobType> jobs;
        switch (selectedCategory) {
            case AVAILABLE:
                jobs = getAvailableJobs(jobsManager);
                break;
            case UNLOCKED:
                jobs = getUnlockedJobs(jobsManager);
                break;
            case LOCKED:
                jobs = getLockedJobs(jobsManager);
                break;
            case BUILDING:
                // Calculate content height for building category
                return calculateBuildingContentHeight(contentWidth, availableHeight);
            default:
                return 0; // No scrolling needed for other categories
        }

        if (jobs.isEmpty()) {
            return 0;
        }

        // Calculate total content height
        int cardsPerRow = Math.max(1, Math.min(2, (contentWidth - SPACING_SM * 2) / (200 + CARD_SPACING)));
        int totalRows = (jobs.size() + cardsPerRow - 1) / cardsPerRow;
        int totalHeight = totalRows * (CARD_HEIGHT + CARD_SPACING) + SPACING_SM;

        // Return max scroll (total height - available height)
        return Math.max(0, totalHeight - availableHeight);
    }

    /**
     * Calculates the content height for the compact building category design.
     */
    private int calculateBuildingContentHeight(int contentWidth, int availableHeight) {
        int totalHeight = 0;

        // Compact explanation section
        totalHeight += 60; // Fixed height for explanation + spacing

        // Compact job requirements section - use actual calculated height
        totalHeight += calculateJobRequirementsHeight();

        // Return max scroll (total height - available height)
        return Math.max(0, totalHeight - availableHeight);
    }

    /**
     * Opens the building request selection screen.
     */
    private void openBuildingRequestScreen() {
        // Get player town from client-side manager
        Town playerTown = null;
        if (client.player != null) {
            playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
        }

        if (playerTown == null) {
            setStatus("You must be in a town to request building approval", TEXT_ERROR);
            return;
        }

        // Check permission before opening building request screen
        if (!PermissionChecker.checkPermissionOrShowDenied(this, playerTown, client.player.getUuid(),
                PermissionChecker.Permissions.JOB,
                PermissionChecker.Permissions.CAN_REQUEST_BUILDING_APPROVAL,
                PermissionChecker.FeatureNames.JOB_SYSTEM)) {
            return; // Permission denied screen was shown
        }

        // Permission granted, create the building selection screen
        this.client.setScreen(new BuildingRequestSelectionScreen(this, playerTown));
    }

    @Override
    public void resize(net.minecraft.client.MinecraftClient client, int width, int height) {
        super.resize(client, width, height);

        // Recalculate panel dimensions when screen resizes
        panelWidth = Math.min(width - 20, 800);  // Responsive width like MyTownScreen
        panelHeight = height - 20;               // Full height like MyTownScreen
        leftX = (width - panelWidth) / 2;
        topY = 10;                               // Top positioning like MyTownScreen

        // Reset scroll offset to prevent content from being out of bounds
        scrollOffset = 0;
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }
}
