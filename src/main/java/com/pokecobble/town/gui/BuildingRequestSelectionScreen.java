package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownRequest;
import com.pokecobble.town.client.ClientTownRequestManager;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

import java.util.Arrays;
import java.util.List;

/**
 * Screen for selecting which building to request approval for.
 * Uses MyTownScreen styling for consistency.
 */
public class BuildingRequestSelectionScreen extends Screen {
    private final Screen parent;
    private final Town town;

    // Panel dimensions
    private int panelWidth = 500;
    private int panelHeight = 350;

    // Glass Effect Color Palette - matching MyTownScreen
    private static final int GLASS_PANEL_BG = 0xD0101010;
    private static final int GLASS_HEADER_BG = 0x60404040;
    private static final int GLASS_CONTENT_BG = 0x30000000;
    private static final int GLASS_CARD_BG = 0x40303030;
    private static final int GLASS_CARD_HOVER = 0x60404040;
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;
    private static final int GLASS_LEFT_HIGHLIGHT = 0x20FFFFFF;
    private static final int GLASS_SHADOW = 0x40000000;

    // Text colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;
    private static final int TEXT_MUTED = 0xFF808080;

    // Available building types in order
    private final List<TownRequest.RequestType> availableBuildings = Arrays.asList(
        TownRequest.RequestType.getBuildingOrder()
    );

    // Scrolling
    private int scrollOffset = 0;

    public BuildingRequestSelectionScreen(Screen parent, Town town) {
        super(Text.literal("Request Building Approval"));
        this.parent = parent;
        this.town = town;

        // Check permission before allowing access
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null) {
            if (!PermissionChecker.checkPermissionOrShowDenied(parent, town, client.player.getUuid(),
                    PermissionChecker.Permissions.JOB,
                    PermissionChecker.Permissions.CAN_REQUEST_BUILDING_APPROVAL,
                    PermissionChecker.FeatureNames.JOB_SYSTEM)) {
                return; // Permission denied screen was shown
            }
        }
    }

    @Override
    protected void init() {
        super.init();
        
        // Calculate responsive panel size
        panelWidth = Math.min(500, width - 40);
        panelHeight = Math.min(350, height - 40);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render background
        this.renderBackground(context);

        // Calculate panel position
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Draw main panel with glass effect
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw header
        drawHeader(context, leftX, topY, panelWidth);

        // Draw content
        drawContent(context, mouseX, mouseY, leftX, topY, panelWidth, panelHeight);

        // Draw action buttons
        drawActionButtons(context, mouseX, mouseY, leftX, topY, panelWidth, panelHeight);

        super.render(context, mouseX, mouseY, delta);
    }

    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Main panel background
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);
        
        // Glass highlights
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y, x + 1, y + height, GLASS_LEFT_HIGHLIGHT);
        
        // Glass shadow
        context.fill(x + 1, y + height - 1, x + width, y + height, GLASS_SHADOW);
        context.fill(x + width - 1, y + 1, x + width, y + height, GLASS_SHADOW);
    }

    private void drawHeader(DrawContext context, int x, int y, int width) {
        int headerHeight = 32;
        
        // Header background
        context.fill(x + 5, y + 5, x + width - 5, y + 5 + headerHeight, GLASS_HEADER_BG);
        context.fill(x + 5, y + 5, x + width - 5, y + 6, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 5, y + 5, x + 6, y + 5 + headerHeight, GLASS_LEFT_HIGHLIGHT);

        // Header text
        String headerText = "Select Building to Request";
        int headerTextX = x + 15;
        int headerTextY = y + 5 + (headerHeight - 8) / 2;
        context.drawTextWithShadow(this.textRenderer, headerText, headerTextX, headerTextY, TEXT_PRIMARY);

        // Town info
        String townInfo = "Town: " + town.getName();
        int townInfoWidth = this.textRenderer.getWidth(townInfo);
        int townInfoX = x + width - 15 - townInfoWidth;
        context.drawTextWithShadow(this.textRenderer, townInfo, townInfoX, headerTextY, TEXT_SECONDARY);
    }

    private void drawContent(DrawContext context, int mouseX, int mouseY, int x, int y, int width, int height) {
        int contentY = y + 45; // After header
        int contentHeight = height - 85; // Leave space for buttons
        int contentWidth = width - 10;

        // Content background - matching MyTownScreen list area
        context.fill(x + 5, contentY, x + contentWidth, contentY + contentHeight, GLASS_CONTENT_BG);
        context.fill(x + 5, contentY, x + contentWidth, contentY + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 5, contentY, x + 6, contentY + contentHeight, GLASS_LEFT_HIGHLIGHT);

        // Draw header with explanation
        int headerY = contentY + 8;
        context.drawTextWithShadow(this.textRenderer, Text.literal("Building Request Order").formatted(net.minecraft.util.Formatting.BOLD),
                x + 15, headerY, TEXT_PRIMARY);
        context.drawTextWithShadow(this.textRenderer, "Buildings must be requested in order. Farm Building must be approved first.",
                x + 15, headerY + 12, TEXT_MUTED);

        // Calculate list area dimensions - matching MyTownScreen pattern
        int listAreaY = contentY + 35; // More space for header
        int listAreaHeight = contentHeight - 45; // Adjust for header
        int leftMargin = x + 5;

        // Draw list area background - matching MyTownScreen
        context.fill(leftMargin, listAreaY, leftMargin + contentWidth - 10, listAreaY + listAreaHeight, 0x30000000);
        context.fill(leftMargin, listAreaY, leftMargin + contentWidth - 10, listAreaY + 1, 0x40FFFFFF); // Top border
        context.fill(leftMargin, listAreaY, leftMargin + 1, listAreaY + listAreaHeight, 0x40FFFFFF); // Left border

        // Calculate card dimensions - matching MyTownScreen job cards
        int cardHeight = 28; // Same as job cards base height
        int cardSpacing = 2; // Same as job cards
        int cardWidth = contentWidth - 25; // Same as job cards

        // Calculate total height for scrollbar
        int totalHeight = availableBuildings.size() * (cardHeight + cardSpacing);
        int maxScroll = Math.max(0, totalHeight - listAreaHeight);

        // Draw scrollbar if needed - matching MyTownScreen pattern
        if (maxScroll > 0) {
            int scrollbarX = leftMargin + contentWidth - 15;

            // Draw scrollbar track
            context.fill(scrollbarX, listAreaY, scrollbarX + 4, listAreaY + listAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(40, listAreaHeight * listAreaHeight / (totalHeight + listAreaHeight));
            float scrollRatio = (float)scrollOffset / maxScroll;
            int scrollbarY = listAreaY + (int)((listAreaHeight - scrollbarHeight) * scrollRatio);

            // Ensure scrollbar doesn't go out of bounds
            scrollbarY = Math.max(listAreaY, Math.min(scrollbarY, listAreaY + listAreaHeight - scrollbarHeight));

            // Draw scrollbar handle
            context.fill(scrollbarX, scrollbarY, scrollbarX + 4, scrollbarY + scrollbarHeight, 0xC0FFFFFF);
        }

        // Apply scissor to clip content to visible area - matching MyTownScreen
        context.enableScissor(
            leftMargin + 5,
            listAreaY,
            leftMargin + contentWidth - 20,
            listAreaY + listAreaHeight
        );

        // Draw building list with proper positioning
        int buildingY = listAreaY - scrollOffset;

        for (int i = 0; i < availableBuildings.size(); i++) {
            TownRequest.RequestType buildingType = availableBuildings.get(i);
            int cardY = buildingY + i * (cardHeight + cardSpacing);

            // Skip if card is outside visible area
            if (cardY + cardHeight < listAreaY || cardY > listAreaY + listAreaHeight) {
                continue;
            }

            drawBuildingCard(context, mouseX, mouseY, buildingType, leftMargin + 5, cardY, cardWidth, cardHeight, listAreaY, listAreaHeight);
        }

        context.disableScissor();
    }

    private void drawBuildingCard(DrawContext context, int mouseX, int mouseY, TownRequest.RequestType buildingType,
                                 int cardX, int cardY, int cardWidth, int cardHeight, int listAreaY, int listAreaHeight) {
        
        boolean isHovered = mouseX >= cardX && mouseX <= cardX + cardWidth &&
                           mouseY >= cardY && mouseY <= cardY + cardHeight &&
                           mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;

        // Check if there's already a pending request for this building
        boolean hasPendingRequest = ClientTownRequestManager.getInstance()
            .hasPendingRequest(town.getId(), buildingType);

        // Check if this building can be requested (building order)
        boolean canRequest = town.canRequestBuilding(buildingType);
        boolean isApproved = town.isBuildingApproved(buildingType);

        // Card background - different colors for different states
        int cardColor;
        if (isApproved) {
            cardColor = 0x4000FF00; // Green tint for approved
        } else if (hasPendingRequest) {
            cardColor = 0x40808080; // Gray for pending
        } else if (!canRequest) {
            cardColor = 0x40FF0000; // Red tint for locked
        } else {
            cardColor = isHovered ? GLASS_CARD_HOVER : GLASS_CARD_BG; // Normal for available
        }
        context.fill(cardX, cardY, cardX + cardWidth, cardY + cardHeight, cardColor);

        // Card highlights
        if (isHovered && !hasPendingRequest && canRequest && !isApproved) {
            context.fill(cardX, cardY, cardX + cardWidth, cardY + 1, GLASS_TOP_HIGHLIGHT);
            context.fill(cardX, cardY, cardX + 1, cardY + cardHeight, GLASS_LEFT_HIGHLIGHT);
        }

        // Building icon and name - compact layout for 28px height
        String icon = buildingType.getIcon();
        String name = buildingType.getDisplayName();
        int nameColor = isApproved ? 0xFF00FF00 : (!canRequest ? 0xFFFF0000 : TEXT_PRIMARY);
        context.drawTextWithShadow(this.textRenderer, icon, cardX + 8, cardY + 10, nameColor);
        context.drawTextWithShadow(this.textRenderer, name, cardX + 26, cardY + 10, nameColor);

        // Status indicator on the right side
        String statusText;
        int statusColor;

        if (isApproved) {
            statusText = "✅ Approved";
            statusColor = 0xFF00FF00;
        } else if (hasPendingRequest) {
            statusText = "⏳ Pending";
            statusColor = 0xFFFF9800;
        } else if (!canRequest) {
            // Find what building is needed first
            TownRequest.RequestType nextBuilding = town.getNextAvailableBuilding();
            if (nextBuilding != null && nextBuilding != buildingType) {
                statusText = "🔒 Locked";
            } else {
                statusText = "🔒 Locked";
            }
            statusColor = 0xFFFF0000;
        } else {
            statusText = isHovered ? "Click to Request" : "Available";
            statusColor = isHovered ? TEXT_PRIMARY : TEXT_MUTED;
        }

        int statusWidth = this.textRenderer.getWidth(statusText);
        int statusX = cardX + cardWidth - statusWidth - 8;
        context.drawTextWithShadow(this.textRenderer, statusText, statusX, cardY + 10, statusColor);
    }

    private void drawActionButtons(DrawContext context, int mouseX, int mouseY, int x, int y, int width, int height) {
        int buttonHeight = 24;
        int buttonY = y + height - 35;
        
        // Back button
        int backWidth = 60;
        int backX = x + 10;
        
        boolean backHovered = mouseX >= backX && mouseX <= backX + backWidth &&
                             mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        
        int backColor = backHovered ? GLASS_CARD_HOVER : GLASS_CARD_BG;
        context.fill(backX, buttonY, backX + backWidth, buttonY + buttonHeight, backColor);
        
        if (backHovered) {
            context.fill(backX, buttonY, backX + backWidth, buttonY + 1, GLASS_TOP_HIGHLIGHT);
            context.fill(backX, buttonY, backX + 1, buttonY + buttonHeight, GLASS_LEFT_HIGHLIGHT);
        }
        
        context.drawCenteredTextWithShadow(this.textRenderer, "Back", backX + backWidth / 2, buttonY + 8, TEXT_PRIMARY);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button != 0) return super.mouseClicked(mouseX, mouseY, button);
        
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;
        
        // Check back button
        int buttonY = topY + panelHeight - 35;
        int backWidth = 60;
        int backX = leftX + 10;
        
        if (mouseX >= backX && mouseX <= backX + backWidth &&
            mouseY >= buttonY && mouseY <= buttonY + 24) {
            SoundUtil.playButtonClickSound();
            this.close();
            return true;
        }
        
        // Check building cards - matching MyTownScreen pattern
        int contentY = topY + 45;
        int listAreaY = contentY + 10;
        int listAreaHeight = panelHeight - 85 - 20; // Same calculation as in drawContent
        int leftMargin = leftX + 5;
        int contentWidth = panelWidth - 10;

        // Check if click is in list area
        if (mouseX >= leftMargin + 5 && mouseX <= leftMargin + contentWidth - 20 &&
            mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {

            int buildingY = listAreaY - scrollOffset;
            int cardHeight = 28; // Same as job cards
            int cardSpacing = 2; // Same as job cards
            int cardWidth = contentWidth - 25; // Same as job cards

            for (int i = 0; i < availableBuildings.size(); i++) {
                TownRequest.RequestType buildingType = availableBuildings.get(i);
                int cardY = buildingY + i * (cardHeight + cardSpacing);

                // Skip if card is outside visible area
                if (cardY + cardHeight < listAreaY || cardY > listAreaY + listAreaHeight) {
                    continue;
                }

                // Check if click is on this building card
                boolean clickOnCard = mouseX >= leftMargin + 5 && mouseX <= leftMargin + 5 + cardWidth &&
                                     mouseY >= cardY && mouseY <= cardY + cardHeight &&
                                     mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight;

                if (clickOnCard) {
                    // Check if there's already a pending request
                    boolean hasPendingRequest = ClientTownRequestManager.getInstance()
                        .hasPendingRequest(town.getId(), buildingType);

                    // Check if this building can be requested
                    boolean canRequest = town.canRequestBuilding(buildingType);
                    boolean isApproved = town.isBuildingApproved(buildingType);

                    if (!hasPendingRequest && canRequest && !isApproved) {
                        SoundUtil.playButtonClickSound();
                        requestBuilding(buildingType);
                    }
                    return true;
                }
            }
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }

    private void requestBuilding(TownRequest.RequestType buildingType) {
        // Create the building request
        String reason = "Requested via town management interface";
        ClientTownRequestManager.getInstance().createRequest(
            town.getId(), 
            town.getName(), 
            buildingType, 
            reason
        );
        
        // Close this screen and return to parent
        this.close();
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        int contentY = (height - panelHeight) / 2 + 45;
        int listAreaY = contentY + 10;
        int listAreaHeight = panelHeight - 85 - 20;

        if (mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {
            int cardHeight = 28; // Same as job cards
            int cardSpacing = 2; // Same as job cards
            int totalHeight = availableBuildings.size() * (cardHeight + cardSpacing);
            int maxScroll = Math.max(0, totalHeight - listAreaHeight);

            scrollOffset -= (int)(amount * 30);
            scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }
}
