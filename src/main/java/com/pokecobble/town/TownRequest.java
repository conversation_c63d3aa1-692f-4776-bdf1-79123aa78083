package com.pokecobble.town;

import java.util.Date;
import java.util.UUID;

/**
 * Represents a building approval request submitted by a town.
 * These requests are reviewed by administrators to approve or deny building construction.
 */
public class TownRequest {
    
    /**
     * Enum representing different types of building requests.
     */
    public enum RequestType {
        FARM_BUILDING("Farm Building", "Required for Farmer job", "🌾"),
        MINE_SHAFT("Mine Shaft", "Required for Miner job", "⛏"),
        FORGE("Forge", "Required for Blacksmith job", "🔨"),
        MARKET_HALL("Market Hall", "Required for Merchant job", "💼"),
        POLICE_STATION("Police Station", "Required for Police job", "👮"),
        CONSTRUCTION_SITE("Construction Site", "Required for Builder job", "🏗"),
        DESIGN_STUDIO("Design Studio", "Required for Architect job", "📐"),
        TOWN_HALL_EXPANSION("Town Hall Expansion", "Required for Mayor's Advisor job", "🏛");

        private final String displayName;
        private final String description;
        private final String icon;

        RequestType(String displayName, String description, String icon) {
            this.displayName = displayName;
            this.description = description;
            this.icon = icon;
        }

        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
        public String getIcon() { return icon; }

        /**
         * Gets the building order - buildings must be requested in this sequence.
         * Farm Building must be first, followed by others in order.
         *
         * @return Array of building types in the required order
         */
        public static RequestType[] getBuildingOrder() {
            return new RequestType[] {
                FARM_BUILDING,      // Must be first
                MINE_SHAFT,
                FORGE,
                MARKET_HALL,
                POLICE_STATION,
                CONSTRUCTION_SITE,
                DESIGN_STUDIO,
                TOWN_HALL_EXPANSION // Last
            };
        }
    }

    /**
     * Enum representing the status of a building request.
     */
    public enum RequestStatus {
        PENDING("Pending Review", "⏳"),
        APPROVED("Approved", "✅"),
        REJECTED("Rejected", "❌");

        private final String displayName;
        private final String icon;

        RequestStatus(String displayName, String icon) {
            this.displayName = displayName;
            this.icon = icon;
        }

        public String getDisplayName() { return displayName; }
        public String getIcon() { return icon; }
    }

    private final UUID requestId;
    private final UUID townId;
    private final String townName;
    private final RequestType requestType;
    private RequestStatus status;
    private final Date requestDate;
    private Date responseDate;
    private final UUID requestedByPlayer;
    private String requestedByPlayerName;
    private UUID reviewedByAdmin;
    private String reviewedByAdminName;
    private String adminNotes;
    private String requestReason;

    /**
     * Creates a new TownRequest instance.
     */
    public TownRequest(UUID townId, String townName, RequestType requestType, UUID requestedByPlayer, String requestedByPlayerName) {
        this.requestId = UUID.randomUUID();
        this.townId = townId;
        this.townName = townName;
        this.requestType = requestType;
        this.status = RequestStatus.PENDING;
        this.requestDate = new Date();
        this.requestedByPlayer = requestedByPlayer;
        this.requestedByPlayerName = requestedByPlayerName != null ? requestedByPlayerName : "";
        this.requestReason = "";
        this.adminNotes = "";
    }

    /**
     * Creates a TownRequest from existing data (for loading from storage).
     */
    public TownRequest(UUID requestId, UUID townId, String townName, RequestType requestType, RequestStatus status,
                      Date requestDate, Date responseDate, UUID requestedByPlayer, String requestedByPlayerName,
                      UUID reviewedByAdmin, String reviewedByAdminName, String adminNotes, String requestReason) {
        this.requestId = requestId;
        this.townId = townId;
        this.townName = townName;
        this.requestType = requestType;
        this.status = status;
        this.requestDate = requestDate;
        this.responseDate = responseDate;
        this.requestedByPlayer = requestedByPlayer;
        this.requestedByPlayerName = requestedByPlayerName;
        this.reviewedByAdmin = reviewedByAdmin;
        this.reviewedByAdminName = reviewedByAdminName;
        this.adminNotes = adminNotes != null ? adminNotes : "";
        this.requestReason = requestReason != null ? requestReason : "";
    }

    // Getters
    public UUID getRequestId() { return requestId; }
    public UUID getTownId() { return townId; }
    public String getTownName() { return townName; }
    public RequestType getRequestType() { return requestType; }
    public RequestStatus getStatus() { return status; }
    public Date getRequestDate() { return requestDate; }
    public Date getResponseDate() { return responseDate; }
    public UUID getRequestedByPlayer() { return requestedByPlayer; }
    public String getRequestedByPlayerName() { return requestedByPlayerName; }
    public UUID getReviewedByAdmin() { return reviewedByAdmin; }
    public String getReviewedByAdminName() { return reviewedByAdminName; }
    public String getAdminNotes() { return adminNotes; }
    public String getRequestReason() { return requestReason; }

    // Setters for mutable fields
    public void setRequestReason(String requestReason) {
        this.requestReason = requestReason != null ? requestReason : "";
    }

    public void setRequestedByPlayerName(String requestedByPlayerName) {
        this.requestedByPlayerName = requestedByPlayerName;
    }

    /**
     * Approves the request with admin information.
     */
    public void approve(UUID adminId, String adminName, String notes) {
        this.status = RequestStatus.APPROVED;
        this.responseDate = new Date();
        this.reviewedByAdmin = adminId;
        this.reviewedByAdminName = adminName;
        this.adminNotes = notes != null ? notes : "";
    }

    /**
     * Rejects the request with admin information.
     */
    public void reject(UUID adminId, String adminName, String notes) {
        this.status = RequestStatus.REJECTED;
        this.responseDate = new Date();
        this.reviewedByAdmin = adminId;
        this.reviewedByAdminName = adminName;
        this.adminNotes = notes != null ? notes : "";
    }

    /**
     * Checks if the request is still pending.
     */
    public boolean isPending() {
        return status == RequestStatus.PENDING;
    }

    /**
     * Checks if the request has been approved.
     */
    public boolean isApproved() {
        return status == RequestStatus.APPROVED;
    }

    /**
     * Checks if the request has been rejected.
     */
    public boolean isRejected() {
        return status == RequestStatus.REJECTED;
    }

    /**
     * Gets a formatted string representation of the request date.
     */
    public String getFormattedRequestDate() {
        return requestDate.toString(); // Can be improved with proper date formatting
    }

    /**
     * Gets a formatted string representation of the response date.
     */
    public String getFormattedResponseDate() {
        return responseDate != null ? responseDate.toString() : "N/A";
    }

    @Override
    public String toString() {
        return String.format("TownRequest{id=%s, town=%s, type=%s, status=%s, requestedBy=%s}",
                requestId, townName, requestType.getDisplayName(), status.getDisplayName(), requestedByPlayerName);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TownRequest that = (TownRequest) obj;
        return requestId.equals(that.requestId);
    }

    @Override
    public int hashCode() {
        return requestId.hashCode();
    }
}
