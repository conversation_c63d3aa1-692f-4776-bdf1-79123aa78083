package com.pokecobble.town;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Manages town building approval requests.
 * Handles creation, approval, rejection, and persistence of requests.
 */
public class TownRequestManager {
    private static TownRequestManager instance;
    private final Map<UUID, TownRequest> requests = new ConcurrentHashMap<>();
    private MinecraftServer server;

    private TownRequestManager() {}

    public static TownRequestManager getInstance() {
        if (instance == null) {
            instance = new TownRequestManager();
        }
        return instance;
    }

    /**
     * Initializes the manager with the server instance.
     */
    public void initialize(MinecraftServer server) {
        this.server = server;
        loadRequests();

        // Schedule periodic cleanup (every 24 hours)
        schedulePeriodicCleanup();
    }

    /**
     * Schedules periodic cleanup of old requests.
     */
    private void schedulePeriodicCleanup() {
        // This will be called from the server tick event in ServerInitializer
        // We'll add a method that can be called periodically
    }

    /**
     * Creates a new building approval request.
     */
    public boolean createRequest(UUID townId, String townName, TownRequest.RequestType requestType,
                               UUID requestedByPlayer, String requestedByPlayerName, String reason) {
        try {
            // Check if there's already a pending request for this town and building type
            boolean hasPendingRequest = requests.values().stream()
                .anyMatch(request -> request.getTownId().equals(townId)
                    && request.getRequestType() == requestType
                    && request.isPending());

            if (hasPendingRequest) {
                Pokecobbleclaim.LOGGER.warn("Town {} already has a pending request for {}", townName, requestType.getDisplayName());
                return false;
            }

            // Check building order prerequisites
            com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTown(townId);
            if (town != null && !town.canRequestBuilding(requestType)) {
                Pokecobbleclaim.LOGGER.warn("Town {} cannot request {} - prerequisites not met", townName, requestType.getDisplayName());
                return false;
            }

            // Create new request
            TownRequest request = new TownRequest(townId, townName, requestType, requestedByPlayer, requestedByPlayerName);
            if (reason != null && !reason.trim().isEmpty()) {
                request.setRequestReason(reason.trim());
            }

            requests.put(request.getRequestId(), request);
            saveRequests();

            Pokecobbleclaim.LOGGER.info("Created building approval request: {} for town {} by player {}", 
                requestType.getDisplayName(), townName, requestedByPlayerName);

            return true;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error creating building approval request for town {}: {}", townName, e.getMessage());
            return false;
        }
    }

    /**
     * Approves a building request.
     */
    public boolean approveRequest(UUID requestId, UUID adminId, String adminName, String notes) {
        try {
            TownRequest request = requests.get(requestId);
            if (request == null) {
                Pokecobbleclaim.LOGGER.warn("Request {} not found for approval", requestId);
                return false;
            }

            if (!request.isPending()) {
                Pokecobbleclaim.LOGGER.warn("Request {} is not pending, cannot approve", requestId);
                return false;
            }

            request.approve(adminId, adminName, notes);
            saveRequests();

            // Mark the building as approved in the town
            com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTown(request.getTownId());
            if (town != null) {
                town.approveBuilding(request.getRequestType());
                com.pokecobble.town.data.TownDataStorage.saveTown(town);

                // Sync building data to all town members
                syncBuildingDataToTownMembers(town);

                Pokecobbleclaim.LOGGER.info("Marked building {} as approved for town {}",
                    request.getRequestType().getDisplayName(), town.getName());
            }

            Pokecobbleclaim.LOGGER.info("Approved building request {} for town {} by admin {}",
                request.getRequestType().getDisplayName(), request.getTownName(), adminName);

            return true;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error approving request {}: {}", requestId, e.getMessage());
            return false;
        }
    }

    /**
     * Rejects a building request.
     */
    public boolean rejectRequest(UUID requestId, UUID adminId, String adminName, String notes) {
        try {
            TownRequest request = requests.get(requestId);
            if (request == null) {
                Pokecobbleclaim.LOGGER.warn("Request {} not found for rejection", requestId);
                return false;
            }

            if (!request.isPending()) {
                Pokecobbleclaim.LOGGER.warn("Request {} is not pending, cannot reject", requestId);
                return false;
            }

            request.reject(adminId, adminName, notes);
            saveRequests();

            Pokecobbleclaim.LOGGER.info("Rejected building request {} for town {} by admin {}", 
                request.getRequestType().getDisplayName(), request.getTownName(), adminName);

            return true;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error rejecting request {}: {}", requestId, e.getMessage());
            return false;
        }
    }

    /**
     * Gets all pending requests.
     */
    public List<TownRequest> getPendingRequests() {
        return requests.values().stream()
            .filter(TownRequest::isPending)
            .sorted(Comparator.comparing(TownRequest::getRequestDate))
            .collect(Collectors.toList());
    }

    /**
     * Gets all requests.
     */
    public List<TownRequest> getAllRequests() {
        return new ArrayList<>(requests.values());
    }

    /**
     * Gets requests for a specific town.
     */
    public List<TownRequest> getRequestsForTown(UUID townId) {
        return requests.values().stream()
            .filter(request -> request.getTownId().equals(townId))
            .sorted(Comparator.comparing(TownRequest::getRequestDate).reversed())
            .collect(Collectors.toList());
    }

    /**
     * Gets a specific request by ID.
     */
    public TownRequest getRequest(UUID requestId) {
        return requests.get(requestId);
    }

    /**
     * Removes old completed requests (older than 30 days).
     */
    public void cleanupOldRequests() {
        try {
            com.pokecobble.town.data.TownRequestDataStorage.cleanupOldRequests(requests, 30);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error cleaning up old requests: {}", e.getMessage());
        }
    }

    /**
     * Performs periodic maintenance tasks.
     * Should be called periodically from server tick events.
     */
    public void performPeriodicMaintenance(long serverTicks) {
        // Cleanup old requests every 24 hours (1,728,000 ticks)
        if (serverTicks % 1728000 == 0) {
            cleanupOldRequests();
        }

        // Create backup every 6 hours (432,000 ticks)
        if (serverTicks % 432000 == 0) {
            try {
                com.pokecobble.town.data.TownRequestDataStorage.createBackup();
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error creating request backup: {}", e.getMessage());
            }
        }
    }

    /**
     * Loads requests from storage.
     */
    private void loadRequests() {
        try {
            Map<UUID, TownRequest> loadedRequests = com.pokecobble.town.data.TownRequestDataStorage.loadRequests();
            requests.clear();

            // Migrate any existing requests that might have null string fields
            boolean needsSave = false;
            for (TownRequest request : loadedRequests.values()) {
                if (migrateRequestData(request)) {
                    needsSave = true;
                }
            }

            requests.putAll(loadedRequests);

            // Save migrated data if needed
            if (needsSave) {
                saveRequests();
                Pokecobbleclaim.LOGGER.info("Migrated and saved town request data to fix null string fields");
            }

            Pokecobbleclaim.LOGGER.info("Loaded {} town building requests from storage", requests.size());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load town requests: " + e.getMessage());
        }
    }

    /**
     * Migrates request data to fix any null string fields.
     * @param request The request to migrate
     * @return true if the request was modified, false otherwise
     */
    private boolean migrateRequestData(TownRequest request) {
        boolean modified = false;

        // Fix null requestedByPlayerName
        if (request.getRequestedByPlayerName() == null) {
            request.setRequestedByPlayerName("");
            modified = true;
        }

        // Fix null requestReason
        if (request.getRequestReason() == null) {
            request.setRequestReason("");
            modified = true;
        }

        // Note: adminNotes and reviewedByAdminName are handled by the TownRequest constructor
        // but we can't easily fix them here since they don't have setters
        // The network handler null checks will handle these cases

        return modified;
    }

    /**
     * Saves requests to storage.
     */
    private void saveRequests() {
        try {
            com.pokecobble.town.data.TownRequestDataStorage.saveRequests(requests);
            Pokecobbleclaim.LOGGER.debug("Saved {} town building requests to storage", requests.size());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save town requests: " + e.getMessage());
        }
    }

    /**
     * Gets the server instance.
     */
    public MinecraftServer getServer() {
        return server;
    }

    /**
     * Checks if a player has permission to create building requests.
     */
    public boolean canPlayerCreateRequest(UUID playerId) {
        if (server == null) return false;
        
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) return false;

        Town playerTown = TownManager.getInstance().getPlayerTown(playerId);
        if (playerTown == null) return false;

        TownPlayer townPlayer = playerTown.getPlayer(playerId);
        if (townPlayer == null) return false;

        return townPlayer.hasPermission("Job", "Can request building approval");
    }

    /**
     * Checks if a player has admin permissions to approve/reject requests.
     */
    public boolean canPlayerManageRequests(UUID playerId) {
        if (server == null) return false;
        
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) return false;

        // Check if player is server operator
        return server.getPlayerManager().isOperator(player.getGameProfile());
    }

    /**
     * Syncs building data to all members of a town.
     */
    private void syncBuildingDataToTownMembers(com.pokecobble.town.Town town) {
        try {
            // Get server instance from TownManager
            net.minecraft.server.MinecraftServer server = com.pokecobble.town.TownManager.getInstance().getServer();
            if (server == null) {
                Pokecobbleclaim.LOGGER.warn("Server instance not available for building data sync");
                return;
            }

            // Mark building aspect as changed
            town.markChanged(com.pokecobble.town.Town.ASPECT_BUILDINGS);

            // Get all online town members
            java.util.List<java.util.UUID> playerIds = town.getPlayers();
            int syncedCount = 0;

            for (java.util.UUID playerId : playerIds) {
                net.minecraft.server.network.ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    // Use the correct sync method
                    com.pokecobble.town.network.town.TownDataSynchronizer.syncTownData(server, town);
                    syncedCount++;
                }
            }

            Pokecobbleclaim.LOGGER.debug("Synced building data to {} online members of town {}",
                syncedCount, town.getName());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing building data to town members", e);
        }
    }
}
