package com.pokecobble.town.network;

import net.minecraft.util.Identifier;

/**
 * Constants for network communication.
 * Centralizes all packet identifiers and network-related constants.
 */
public class NetworkConstants {
    // Mod ID for creating identifiers
    public static final String MOD_ID = "pokecobbleclaim";

    // Town data packets
    public static final Identifier TOWN_DATA_REQUEST = new Identifier(MOD_ID, "town_data_request");
    public static final Identifier TOWN_DATA_RESPONSE = new Identifier(MOD_ID, "town_data_response");
    public static final Identifier TOWN_LIST_REQUEST = new Identifier(MOD_ID, "town_list_request");
    public static final Identifier TOWN_LIST_RESPONSE = new Identifier(MOD_ID, "town_list_response");

    // Town management packets
    public static final Identifier TOWN_CREATE_REQUEST = new Identifier(MOD_ID, "town_create_request");
    public static final Identifier TOWN_CREATE_RESPONSE = new Identifier(MOD_ID, "town_create_response");
    public static final Identifier TOWN_JOIN_REQUEST = new Identifier(MOD_ID, "town_join_request");
    public static final Identifier TOWN_JOIN_RESPONSE = new Identifier(MOD_ID, "town_join_response");
    public static final Identifier ADMIN_JOIN_REQUEST = new Identifier(MOD_ID, "admin_join_request");
    public static final Identifier TOWN_LEAVE_REQUEST = new Identifier(MOD_ID, "town_leave_request");
    public static final Identifier TOWN_LEAVE_RESPONSE = new Identifier(MOD_ID, "town_leave_response");
    public static final Identifier TOWN_KICK_PLAYER_REQUEST = new Identifier(MOD_ID, "town_kick_player_request");
    public static final Identifier TOWN_KICK_PLAYER_RESPONSE = new Identifier(MOD_ID, "town_kick_player_response");
    public static final Identifier TOWN_BAN_PLAYER_REQUEST = new Identifier(MOD_ID, "town_ban_player_request");
    public static final Identifier TOWN_BAN_PLAYER_RESPONSE = new Identifier(MOD_ID, "town_ban_player_response");

    // Town player list updates
    public static final Identifier TOWN_PLAYER_LIST_UPDATE = new Identifier(MOD_ID, "town_player_list_update");

    // Town building updates
    public static final Identifier TOWN_BUILDING_UPDATE = new Identifier(MOD_ID, "town_building_update");

    // Player data packets
    public static final Identifier PLAYER_DATA_REQUEST = new Identifier(MOD_ID, "player_data_request");
    public static final Identifier PLAYER_DATA_RESPONSE = new Identifier(MOD_ID, "player_data_response");
    public static final Identifier PLAYER_PERMISSIONS_UPDATE = new Identifier(MOD_ID, "player_permissions_update");
    public static final Identifier PLAYER_DATA_SYNC = new Identifier(MOD_ID, "player_data_sync");
    public static final Identifier PLAYER_RANK_CHANGE_REQUEST = new Identifier(MOD_ID, "player_rank_change_request");
    public static final Identifier PLAYER_RANK_CHANGE_RESPONSE = new Identifier(MOD_ID, "player_rank_change_response");
    public static final Identifier PLAYER_RANK_CHANGE_BROADCAST = new Identifier(MOD_ID, "player_rank_change_broadcast");

    // Chunk data packets
    public static final Identifier CHUNK_DATA_REQUEST = new Identifier(MOD_ID, "chunk_data_request");
    public static final Identifier CHUNK_DATA_RESPONSE = new Identifier(MOD_ID, "chunk_data_response");
    public static final Identifier CHUNK_CLAIM_REQUEST = new Identifier(MOD_ID, "chunk_claim_request");
    public static final Identifier CHUNK_CLAIM_RESPONSE = new Identifier(MOD_ID, "chunk_claim_response");
    public static final Identifier CHUNK_UNCLAIM_REQUEST = new Identifier(MOD_ID, "chunk_unclaim_request");
    public static final Identifier CHUNK_UNCLAIM_RESPONSE = new Identifier(MOD_ID, "chunk_unclaim_response");
    public static final Identifier CHUNK_TAG_UPDATE_REQUEST = new Identifier(MOD_ID, "chunk_tag_update_request");
    public static final Identifier CHUNK_TAG_UPDATE_RESPONSE = new Identifier(MOD_ID, "chunk_tag_update_response");
    public static final Identifier CHUNK_CLAIM_SYNC_REQUEST = new Identifier(MOD_ID, "chunk_claim_sync_request");
    public static final Identifier CHUNK_CLAIM_SYNC_RESPONSE = new Identifier(MOD_ID, "chunk_claim_sync_response");

    // Claim tool selection synchronization packets
    public static final Identifier CLAIM_TOOL_SELECTION_UPDATE = new Identifier(MOD_ID, "claim_tool_selection_update");
    public static final Identifier CLAIM_TOOL_SELECTION_BROADCAST = new Identifier(MOD_ID, "claim_tool_selection_broadcast");
    public static final Identifier CLAIM_TOOL_STATE_REQUEST = new Identifier(MOD_ID, "claim_tool_state_request");
    public static final Identifier CLAIM_TOOL_STATE_RESPONSE = new Identifier(MOD_ID, "claim_tool_state_response");
    public static final Identifier CLAIM_TOOL_AUTO_SELECT_REQUEST = new Identifier(MOD_ID, "claim_tool_auto_select_request");
    public static final Identifier CLAIM_TOOL_AUTO_SELECT_RESPONSE = new Identifier(MOD_ID, "claim_tool_auto_select_response");

    // New optimized chunk sync packets
    public static final Identifier TOWN_CHUNK_SYNC_REQUEST = new Identifier(MOD_ID, "town_chunk_sync_request");
    public static final Identifier TOWN_CHUNK_SYNC_RESPONSE = new Identifier(MOD_ID, "town_chunk_sync_response");
    public static final Identifier TOWN_CHUNK_UPDATE = new Identifier(MOD_ID, "town_chunk_update");
    public static final Identifier TOWN_CHUNK_BATCH_UPDATE = new Identifier(MOD_ID, "town_chunk_batch_update");

    // Global chunk synchronization packets
    public static final Identifier GLOBAL_CHUNK_SYNC_REQUEST = new Identifier(MOD_ID, "global_chunk_sync_request");
    public static final Identifier GLOBAL_CHUNK_SYNC_RESPONSE = new Identifier(MOD_ID, "global_chunk_sync_response");
    public static final Identifier GLOBAL_CHUNK_UPDATE = new Identifier(MOD_ID, "global_chunk_update");

    // Claim tag synchronization packets
    public static final Identifier CLAIM_TAG_UPDATE_REQUEST = new Identifier(MOD_ID, "claim_tag_update_request");
    public static final Identifier CLAIM_TAG_UPDATE_RESPONSE = new Identifier(MOD_ID, "claim_tag_update_response");
    public static final Identifier CLAIM_TAG_BROADCAST = new Identifier(MOD_ID, "claim_tag_broadcast");
    public static final Identifier CLAIM_TAG_ALL_SYNC_REQUEST = new Identifier(MOD_ID, "claim_tag_all_sync_request");
    public static final Identifier CLAIM_TAG_ALL_SYNC_BROADCAST = new Identifier(MOD_ID, "claim_tag_all_sync_broadcast");
    public static final Identifier CLAIM_TAG_DATA_REQUEST = new Identifier(MOD_ID, "claim_tag_data_request");
    public static final Identifier CLAIM_TAG_DATA_RESPONSE = new Identifier(MOD_ID, "claim_tag_data_response");



    // Election packets
    public static final Identifier ELECTION_DATA_REQUEST = new Identifier(MOD_ID, "election_data_request");
    public static final Identifier ELECTION_DATA_RESPONSE = new Identifier(MOD_ID, "election_data_response");
    public static final Identifier ELECTION_VOTE_REQUEST = new Identifier(MOD_ID, "election_vote_request");
    public static final Identifier ELECTION_VOTE_RESPONSE = new Identifier(MOD_ID, "election_vote_response");

    // Money packets
    public static final Identifier MONEY_BALANCE_SYNC = new Identifier(MOD_ID, "money_balance_sync");
    public static final Identifier MONEY_BALANCE_REQUEST = new Identifier(MOD_ID, "money_balance_request");
    public static final Identifier MONEY_WATCH_START = new Identifier(MOD_ID, "money_watch_start");
    public static final Identifier MONEY_WATCH_STOP = new Identifier(MOD_ID, "money_watch_stop");
    public static final Identifier MONEY_TRANSFER_REQUEST = new Identifier(MOD_ID, "money_transfer_request");
    public static final Identifier MONEY_TRANSFER_RESPONSE = new Identifier(MOD_ID, "money_transfer_response");

    // Town image packets
    public static final Identifier TOWN_IMAGE_SELECTION_UPDATE = new Identifier(MOD_ID, "town_image_selection_update");
    public static final Identifier TOWN_IMAGE_UPLOAD_REQUEST = new Identifier(MOD_ID, "town_image_upload_request");
    public static final Identifier TOWN_IMAGE_UPLOAD_CHUNK = new Identifier(MOD_ID, "town_image_upload_chunk");
    public static final Identifier TOWN_IMAGE_UPLOAD_COMPLETE = new Identifier(MOD_ID, "town_image_upload_complete");
    public static final Identifier TOWN_IMAGE_UPLOAD_RESPONSE = new Identifier(MOD_ID, "town_image_upload_response");
    public static final Identifier TOWN_IMAGE_DELETE_REQUEST = new Identifier(MOD_ID, "town_image_delete_request");
    public static final Identifier TOWN_IMAGE_DELETE_RESPONSE = new Identifier(MOD_ID, "town_image_delete_response");
    public static final Identifier TOWN_IMAGE_CACHE_CLEAR = new Identifier(MOD_ID, "town_image_cache_clear");
    public static final Identifier TOWN_IMAGE_DATA = new Identifier(MOD_ID, "town_image_data");
    public static final Identifier TOWN_IMAGE_TRANSFER_START = new Identifier(MOD_ID, "town_image_transfer_start");
    public static final Identifier TOWN_IMAGE_CHUNK = new Identifier(MOD_ID, "town_image_chunk");
    public static final Identifier TOWN_IMAGE_TRANSFER_COMPLETE = new Identifier(MOD_ID, "town_image_transfer_complete");

    // New dedicated image selection packets (independent from settings)
    public static final Identifier TOWN_IMAGE_SLOT_UPDATE = new Identifier(MOD_ID, "town_image_slot_update");
    public static final Identifier TOWN_IMAGE_SLOT_SELECT = new Identifier(MOD_ID, "town_image_slot_select");
    public static final Identifier TOWN_IMAGE_SELECTION_SYNC = new Identifier(MOD_ID, "town_image_selection_sync");
    public static final Identifier TOWN_IMAGE_SELECTION_REQUEST = new Identifier(MOD_ID, "town_image_selection_request");

    // Town jobs packets
    public static final Identifier TOWN_JOBS_DATA_REQUEST = new Identifier(MOD_ID, "town_jobs_data_request");
    public static final Identifier TOWN_JOBS_DATA_RESPONSE = new Identifier(MOD_ID, "town_jobs_data_response");
    public static final Identifier TOWN_JOB_UNLOCK_REQUEST = new Identifier(MOD_ID, "town_job_unlock_request");
    public static final Identifier TOWN_JOB_UNLOCK_RESPONSE = new Identifier(MOD_ID, "town_job_unlock_response");
    public static final Identifier TOWN_JOB_APPLY_REQUEST = new Identifier(MOD_ID, "town_job_apply_request");
    public static final Identifier TOWN_JOB_APPLY_RESPONSE = new Identifier(MOD_ID, "town_job_apply_response");

    // Town request packets
    public static final Identifier TOWN_REQUEST_LIST_REQUEST = new Identifier(MOD_ID, "town_request_list_request");
    public static final Identifier TOWN_REQUEST_LIST_RESPONSE = new Identifier(MOD_ID, "town_request_list_response");
    public static final Identifier TOWN_REQUEST_CREATE = new Identifier(MOD_ID, "town_request_create");
    public static final Identifier TOWN_REQUEST_CREATE_RESPONSE = new Identifier(MOD_ID, "town_request_create_response");
    public static final Identifier TOWN_REQUEST_APPROVE = new Identifier(MOD_ID, "town_request_approve");
    public static final Identifier TOWN_REQUEST_REJECT = new Identifier(MOD_ID, "town_request_reject");
    public static final Identifier TOWN_REQUEST_RESPONSE = new Identifier(MOD_ID, "town_request_response");
    public static final Identifier TOWN_REQUEST_UPDATE = new Identifier(MOD_ID, "town_request_update");
    public static final Identifier TOWN_JOB_ADMIN_APPROVE = new Identifier(MOD_ID, "town_job_admin_approve");
    public static final Identifier TOWN_JOB_ADMIN_REJECT = new Identifier(MOD_ID, "town_job_admin_reject");
    public static final Identifier TOWN_JOBS_SYNC = new Identifier(MOD_ID, "town_jobs_sync");

    // Town chat packets
    public static final Identifier TOWN_CHAT_MESSAGE_SEND = new Identifier(MOD_ID, "town_chat_message_send");
    public static final Identifier TOWN_CHAT_MESSAGE_RECEIVE = new Identifier(MOD_ID, "town_chat_message_receive");
    public static final Identifier TOWN_CHAT_HISTORY_REQUEST = new Identifier(MOD_ID, "town_chat_history_request");
    public static final Identifier TOWN_CHAT_HISTORY_RESPONSE = new Identifier(MOD_ID, "town_chat_history_response");
    public static final Identifier TOWN_CHAT_VIEWER_REGISTER = new Identifier(MOD_ID, "town_chat_viewer_register");
    public static final Identifier TOWN_CHAT_VIEWER_UNREGISTER = new Identifier(MOD_ID, "town_chat_viewer_unregister");

    // Security constants
    public static final int MAX_PACKET_SIZE = 32768; // 32KB max packet size
    public static final int MAX_STRING_LENGTH = 8192; // 8KB max string length (increased for town settings)
    public static final int MAX_LIST_SIZE = 1000; // Maximum items in a list
    public static final int MAX_CHUNK_SIZE = 16384; // 16KB max chunk size for image transfers
}
