package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.TownRequest;
import com.pokecobble.town.TownRequestManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;

import java.util.ArrayList;
import java.util.List;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Handles network communication for town building approval requests.
 */
public class TownRequestNetworkHandler {

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_LIST_REQUEST, TownRequestNetworkHandler::handleRequestListRequest);
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_CREATE, TownRequestNetworkHandler::handleCreateRequest);
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_APPROVE, TownRequestNetworkHandler::handleApproveRequest);
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_REJECT, TownRequestNetworkHandler::handleRejectRequest);
        
        Pokecobbleclaim.LOGGER.info("Registered town request network handlers");
    }

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        ClientPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_LIST_RESPONSE, TownRequestNetworkHandler::handleRequestListResponse);
        ClientPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_CREATE_RESPONSE, TownRequestNetworkHandler::handleCreateResponse);
        ClientPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_RESPONSE, TownRequestNetworkHandler::handleRequestResponse);
        ClientPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_REQUEST_UPDATE, TownRequestNetworkHandler::handleRequestUpdate);
    }

    // Server-side handlers

    /**
     * Handles request list requests from clients.
     */
    private static void handleRequestListRequest(MinecraftServer server, ServerPlayerEntity player,
                                               ServerPlayNetworkHandler handler, PacketByteBuf buf, PacketSender sender) {
        try {
            // Check if player has admin permissions
            if (!TownRequestManager.getInstance().canPlayerManageRequests(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player {} tried to request town request list without permission", player.getName().getString());
                return;
            }

            // Get pending requests
            List<TownRequest> pendingRequests = TownRequestManager.getInstance().getPendingRequests();
            sendRequestListResponse(player, pendingRequests);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling request list request from player " + player.getName().getString(), e);
        }
    }

    /**
     * Handles create request from clients.
     */
    private static void handleCreateRequest(MinecraftServer server, ServerPlayerEntity player,
                                          ServerPlayNetworkHandler handler, PacketByteBuf buf, PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            String requestTypeName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            String reason = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Parse request type
            TownRequest.RequestType requestType;
            try {
                requestType = TownRequest.RequestType.valueOf(requestTypeName);
            } catch (IllegalArgumentException e) {
                Pokecobbleclaim.LOGGER.warn("Invalid request type: {}", requestTypeName);
                sendCreateResponse(player, false, "Invalid request type");
                return;
            }

            // Check if player has permission
            if (!TownRequestManager.getInstance().canPlayerCreateRequest(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player {} tried to create request without permission", player.getName().getString());
                sendCreateResponse(player, false, "You don't have permission to create building requests");
                return;
            }

            // Create the request
            boolean success = TownRequestManager.getInstance().createRequest(
                townId, townName, requestType, player.getUuid(), player.getName().getString(), reason);

            if (success) {
                sendCreateResponse(player, true, "Request created successfully");
                // Notify all admins of new request
                notifyAdminsOfNewRequest(server);
            } else {
                sendCreateResponse(player, false, "Failed to create request - may already exist");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling create request from player " + player.getName().getString(), e);
            sendCreateResponse(player, false, "Internal server error");
        }
    }

    /**
     * Handles approve request from clients.
     */
    private static void handleApproveRequest(MinecraftServer server, ServerPlayerEntity player,
                                           ServerPlayNetworkHandler handler, PacketByteBuf buf, PacketSender sender) {
        try {
            UUID requestId = buf.readUuid();
            String notes = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Check if player has admin permissions
            if (!TownRequestManager.getInstance().canPlayerManageRequests(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player {} tried to approve request without permission", player.getName().getString());
                sendRequestResponse(player, false, "You don't have permission to manage requests");
                return;
            }

            // Approve the request
            boolean success = TownRequestManager.getInstance().approveRequest(
                requestId, player.getUuid(), player.getName().getString(), notes);

            if (success) {
                sendRequestResponse(player, true, "Request approved successfully");
                // Notify all admins of the update
                notifyAdminsOfRequestUpdate(server);
            } else {
                sendRequestResponse(player, false, "Failed to approve request");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling approve request from player " + player.getName().getString(), e);
            sendRequestResponse(player, false, "Internal server error");
        }
    }

    /**
     * Handles reject request from clients.
     */
    private static void handleRejectRequest(MinecraftServer server, ServerPlayerEntity player,
                                          ServerPlayNetworkHandler handler, PacketByteBuf buf, PacketSender sender) {
        try {
            UUID requestId = buf.readUuid();
            String notes = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Check if player has admin permissions
            if (!TownRequestManager.getInstance().canPlayerManageRequests(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player {} tried to reject request without permission", player.getName().getString());
                sendRequestResponse(player, false, "You don't have permission to manage requests");
                return;
            }

            // Reject the request
            boolean success = TownRequestManager.getInstance().rejectRequest(
                requestId, player.getUuid(), player.getName().getString(), notes);

            if (success) {
                sendRequestResponse(player, true, "Request rejected successfully");
                // Notify all admins of the update
                notifyAdminsOfRequestUpdate(server);
            } else {
                sendRequestResponse(player, false, "Failed to reject request");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling reject request from player " + player.getName().getString(), e);
            sendRequestResponse(player, false, "Internal server error");
        }
    }

    // Server-side response senders

    /**
     * Sends request list response to a player.
     */
    public static void sendRequestListResponse(ServerPlayerEntity player, List<TownRequest> requests) {
        PacketByteBuf buf = PacketByteBufs.create();
        
        buf.writeInt(requests.size());
        for (TownRequest request : requests) {
            writeRequestData(buf, request);
        }

        ServerPlayNetworking.send(player, NetworkConstants.TOWN_REQUEST_LIST_RESPONSE, buf);
    }

    /**
     * Sends create response to a player.
     */
    public static void sendCreateResponse(ServerPlayerEntity player, boolean success, String message) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeBoolean(success);
        buf.writeString(message, NetworkConstants.MAX_STRING_LENGTH);

        ServerPlayNetworking.send(player, NetworkConstants.TOWN_REQUEST_CREATE_RESPONSE, buf);
    }

    /**
     * Sends request response to a player.
     */
    public static void sendRequestResponse(ServerPlayerEntity player, boolean success, String message) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeBoolean(success);
        buf.writeString(message, NetworkConstants.MAX_STRING_LENGTH);

        ServerPlayNetworking.send(player, NetworkConstants.TOWN_REQUEST_RESPONSE, buf);
    }

    /**
     * Sends a request update to a specific player.
     */
    public static void sendRequestUpdate(ServerPlayerEntity player, TownRequest request) {
        PacketByteBuf buf = PacketByteBufs.create();
        writeRequestData(buf, request);

        ServerPlayNetworking.send(player, NetworkConstants.TOWN_REQUEST_UPDATE, buf);
    }

    /**
     * Syncs all relevant requests to a player when they join.
     */
    public static void syncRequestsToPlayer(ServerPlayerEntity player) {
        try {
            TownRequestManager requestManager = TownRequestManager.getInstance();

            // Get player's town
            com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(player.getUuid());

            // Get requests relevant to this player
            List<TownRequest> relevantRequests = new ArrayList<>();

            // If player can manage requests (admin), send all pending requests
            if (requestManager.canPlayerManageRequests(player.getUuid())) {
                relevantRequests.addAll(requestManager.getPendingRequests());
            }

            // If player is in a town, send requests for their town
            if (playerTown != null) {
                relevantRequests.addAll(requestManager.getRequestsForTown(playerTown.getId()));
            }

            // Remove duplicates
            relevantRequests = relevantRequests.stream().distinct().collect(java.util.stream.Collectors.toList());

            // Send the requests
            if (!relevantRequests.isEmpty()) {
                sendRequestListResponse(player, relevantRequests);
                Pokecobbleclaim.LOGGER.debug("Synced {} requests to player {}", relevantRequests.size(), player.getName().getString());
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing requests to player " + player.getName().getString(), e);
        }
    }

    /**
     * Notifies all admins of a new request.
     */
    private static void notifyAdminsOfNewRequest(MinecraftServer server) {
        List<TownRequest> pendingRequests = TownRequestManager.getInstance().getPendingRequests();

        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            if (TownRequestManager.getInstance().canPlayerManageRequests(player.getUuid())) {
                sendRequestListResponse(player, pendingRequests);
            }
        }
    }

    /**
     * Notifies all admins of a request update.
     */
    private static void notifyAdminsOfRequestUpdate(MinecraftServer server) {
        List<TownRequest> pendingRequests = TownRequestManager.getInstance().getPendingRequests();
        
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            if (TownRequestManager.getInstance().canPlayerManageRequests(player.getUuid())) {
                sendRequestListResponse(player, pendingRequests);
            }
        }
    }

    /**
     * Writes request data to packet buffer.
     */
    private static void writeRequestData(PacketByteBuf buf, TownRequest request) {
        buf.writeUuid(request.getRequestId());
        buf.writeUuid(request.getTownId());
        buf.writeString(request.getTownName() != null ? request.getTownName() : "", NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(request.getRequestType().name(), NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(request.getStatus().name(), NetworkConstants.MAX_STRING_LENGTH);
        buf.writeLong(request.getRequestDate().getTime());
        buf.writeUuid(request.getRequestedByPlayer());
        buf.writeString(request.getRequestedByPlayerName() != null ? request.getRequestedByPlayerName() : "", NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(request.getRequestReason() != null ? request.getRequestReason() : "", NetworkConstants.MAX_STRING_LENGTH);

        // Optional fields
        if (request.getResponseDate() != null) {
            buf.writeBoolean(true);
            buf.writeLong(request.getResponseDate().getTime());
        } else {
            buf.writeBoolean(false);
        }

        if (request.getReviewedByAdmin() != null) {
            buf.writeBoolean(true);
            buf.writeUuid(request.getReviewedByAdmin());
            buf.writeString(request.getReviewedByAdminName() != null ? request.getReviewedByAdminName() : "", NetworkConstants.MAX_STRING_LENGTH);
        } else {
            buf.writeBoolean(false);
        }

        buf.writeString(request.getAdminNotes() != null ? request.getAdminNotes() : "", NetworkConstants.MAX_STRING_LENGTH);
    }

    // Client-side handlers

    /**
     * Handles request list response from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleRequestListResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                PacketByteBuf buf, PacketSender sender) {
        try {
            int requestCount = buf.readInt();
            List<TownRequest> requests = new java.util.ArrayList<>();

            for (int i = 0; i < requestCount; i++) {
                TownRequest request = readRequestData(buf);
                requests.add(request);
            }

            // Update client-side manager
            com.pokecobble.town.client.ClientTownRequestManager.getInstance().updateRequestList(requests);

            Pokecobbleclaim.LOGGER.info("Received {} town requests from server", requestCount);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling request list response", e);
        }
    }

    /**
     * Handles create response from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleCreateResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf, PacketSender sender) {
        try {
            boolean success = buf.readBoolean();
            String message = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Update client-side manager
            com.pokecobble.town.client.ClientTownRequestManager.getInstance().handleCreateResponse(success, message);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling create response", e);
        }
    }

    /**
     * Handles request response from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleRequestResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                            PacketByteBuf buf, PacketSender sender) {
        try {
            boolean success = buf.readBoolean();
            String message = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Update client-side manager
            com.pokecobble.town.client.ClientTownRequestManager.getInstance().handleRequestResponse(success, message);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling request response", e);
        }
    }

    /**
     * Handles request update from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleRequestUpdate(MinecraftClient client, ClientPlayNetworkHandler handler,
                                          PacketByteBuf buf, PacketSender sender) {
        try {
            // Read the updated request data
            TownRequest updatedRequest = readRequestData(buf);

            // Update client-side manager
            com.pokecobble.town.client.ClientTownRequestManager.getInstance().updateRequest(updatedRequest);

            Pokecobbleclaim.LOGGER.info("Updated request {} from server", updatedRequest.getRequestId());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling request update", e);
        }
    }

    /**
     * Reads request data from packet buffer.
     */
    @Environment(EnvType.CLIENT)
    private static TownRequest readRequestData(PacketByteBuf buf) {
        UUID requestId = buf.readUuid();
        UUID townId = buf.readUuid();
        String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
        TownRequest.RequestType requestType = TownRequest.RequestType.valueOf(buf.readString(NetworkConstants.MAX_STRING_LENGTH));
        TownRequest.RequestStatus status = TownRequest.RequestStatus.valueOf(buf.readString(NetworkConstants.MAX_STRING_LENGTH));
        Date requestDate = new Date(buf.readLong());
        UUID requestedByPlayer = buf.readUuid();
        String requestedByPlayerName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
        String requestReason = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
        
        Date responseDate = null;
        if (buf.readBoolean()) {
            responseDate = new Date(buf.readLong());
        }
        
        UUID reviewedByAdmin = null;
        String reviewedByAdminName = null;
        if (buf.readBoolean()) {
            reviewedByAdmin = buf.readUuid();
            reviewedByAdminName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
        }
        
        String adminNotes = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
        
        return new TownRequest(requestId, townId, townName, requestType, status, requestDate, responseDate,
                              requestedByPlayer, requestedByPlayerName, reviewedByAdmin, reviewedByAdminName,
                              adminNotes, requestReason);
    }

    // Client-side request senders

    /**
     * Requests the list of town requests from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestRequestList() {
        PacketByteBuf buf = PacketByteBufs.create();
        NetworkManager.sendToServer(NetworkConstants.TOWN_REQUEST_LIST_REQUEST, buf);
    }

    /**
     * Sends a create request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendCreateRequest(UUID townId, String townName, TownRequest.RequestType requestType, String reason) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(townId);
        buf.writeString(townName, NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(requestType.name(), NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(reason, NetworkConstants.MAX_STRING_LENGTH);
        
        NetworkManager.sendToServer(NetworkConstants.TOWN_REQUEST_CREATE, buf);
    }

    /**
     * Sends an approve request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendApproveRequest(UUID requestId, String notes) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(requestId);
        buf.writeString(notes, NetworkConstants.MAX_STRING_LENGTH);
        
        NetworkManager.sendToServer(NetworkConstants.TOWN_REQUEST_APPROVE, buf);
    }

    /**
     * Sends a reject request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendRejectRequest(UUID requestId, String notes) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(requestId);
        buf.writeString(notes, NetworkConstants.MAX_STRING_LENGTH);
        
        NetworkManager.sendToServer(NetworkConstants.TOWN_REQUEST_REJECT, buf);
    }
}
